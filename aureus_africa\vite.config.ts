import path from 'path';
import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, '.', '');
    return {
      plugins: [react()],
      server: {
        host: '0.0.0.0',
        port: parseInt(process.env.PORT || '8000'),
        proxy: {
          '/api': {
            target: 'http://localhost:8003',
            changeOrigin: true,
            secure: false
          }
        }
      },
      preview: {
        host: '0.0.0.0',
        port: parseInt(process.env.PORT || '8000'),
        strictPort: true,
        allowedHosts: ['aureusafrica-production.up.railway.app', 'localhost']
      },
      build: {
        outDir: 'dist',
        sourcemap: true,
        rollupOptions: {
          output: {
            manualChunks: {
              vendor: ['react', 'react-dom'],
              supabase: ['@supabase/supabase-js']
            }
          }
        }
      },
      define: {
        'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || mode),
        'process.env.API_KEY': JSON.stringify(env.GEMINI_API_KEY || ''),
        'process.env.GEMINI_API_KEY': JSON.stringify(env.GEMINI_API_KEY || '')
      },
      resolve: {
        alias: {
          '@': path.resolve(__dirname, '.'),
        }
      }
    };
});
