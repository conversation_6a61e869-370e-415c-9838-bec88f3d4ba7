import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !serviceKey) {
  console.warn('⚠️ Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY for duplicate check API');
}

const supabase = (supabaseUrl && serviceKey) ? createClient(supabaseUrl, serviceKey) : null;

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    if (!supabase) {
      return res.status(500).json({ error: 'Server not configured' });
    }

    const { email, username } = req.body || {};
    const emailValue = (email || '').trim().toLowerCase();
    const usernameValue = (username || '').trim().toLowerCase();

    const results = {
      emailDuplicate: false,
      usernameDuplicate: false,
    };

    // Check email duplicates in users and telegram_users (email and temp_email)
    if (emailValue) {
      const [usersEmail, telegramEmail] = await Promise.all([
        supabase.from('users').select('id').ilike('email', emailValue).limit(1),
        supabase
          .from('telegram_users')
          .select('telegram_id')
          .or(`email.ilike.${emailValue},temp_email.ilike.${emailValue}`)
          .limit(1),
      ]);

      if (usersEmail.error) {
        console.error('❌ Duplicate check users email error:', usersEmail.error);
        return res.status(500).json({ error: 'Database error (users.email)' });
      }
      if (telegramEmail.error) {
        console.error('❌ Duplicate check telegram email error:', telegramEmail.error);
        return res.status(500).json({ error: 'Database error (telegram_users.email/temp_email)' });
      }

      results.emailDuplicate = (usersEmail.data?.length || 0) > 0 || (telegramEmail.data?.length || 0) > 0;
    }

    // Check username duplicates in users and telegram_users
    if (usernameValue) {
      const [usersUsername, telegramUsername] = await Promise.all([
        supabase.from('users').select('id').ilike('username', usernameValue).limit(1),
        supabase.from('telegram_users').select('telegram_id').ilike('username', usernameValue).limit(1),
      ]);

      if (usersUsername.error) {
        console.error('❌ Duplicate check users username error:', usersUsername.error);
        return res.status(500).json({ error: 'Database error (users.username)' });
      }
      if (telegramUsername.error) {
        console.error('❌ Duplicate check telegram username error:', telegramUsername.error);
        return res.status(500).json({ error: 'Database error (telegram_users.username)' });
      }

      results.usernameDuplicate = (usersUsername.data?.length || 0) > 0 || (telegramUsername.data?.length || 0) > 0;
    }

    return res.status(200).json({ success: true, ...results });
  } catch (error) {
    console.error('❌ Duplicate check API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

