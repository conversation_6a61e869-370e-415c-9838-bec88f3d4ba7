import React, { useState, useEffect, useCallback, useRef } from 'react'
import { registerUserWithEmail, supabase } from '../lib/supabase'
import { trackReferralClick, trackReferralConversion } from '../lib/referralTracking'
import { hashPassword, validatePasswordStrength, getPasswordStrengthScore } from '../lib/passwordSecurity'
import { resendEmailService } from '../lib/resendEmailService'

interface EmailRegistrationFormProps {
  onRegistrationSuccess: (user: any) => void
  onSwitchToLogin: () => void
}

export const EmailRegistrationForm: React.FC<EmailRegistrationFormProps> = ({
  onRegistrationSuccess,
  onSwitchToLogin
}) => {
  const [registrationMode, setRegistrationMode] = useState<'new' | 'telegram'>('new')
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    username: '',
    phone: '',
    countryOfResidence: 'ZAF',
    telegramId: '',
    sponsorUsername: '',
    telegramUsername: '',
    hasTelegramAccount: false,
    campaignSource: ''
  })
  const [emailVerification, setEmailVerification] = useState({
    isVerified: false,
    isModalOpen: false,
    tempUserId: null as number | null,
    isEmailValid: false,
    verificationCode: undefined as string | undefined,
    codeExpiry: undefined as number | undefined
  })
  const [telegramVerification, setTelegramVerification] = useState<{
    verified: boolean
    telegramUser: any
    sponsorInfo: any
    loading: boolean
    error: string
  }>({
    verified: false,
    telegramUser: null,
    sponsorInfo: null,
    loading: false,
    error: ''
  })
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [generalError, setGeneralError] = useState('')
  const [validating, setValidating] = useState<{ email: boolean; username: boolean }>({ email: false, username: false })


  // Refs for debouncing validation
  const emailCheckTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const usernameCheckTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Debounced email duplicate check
  const checkEmailDuplicate = useCallback(async (email: string) => {
    try {
      const emailValue = email.trim().toLowerCase()
      console.log('🔍 Checking for duplicate email in both users and telegram_users tables:', emailValue)

      // Query users and telegram_users (checking both email and temp_email) with case-insensitive match
      const [usersResult, telegramUsersResult] = await Promise.all([
        supabase
          .from('users')
          .select('email')
          .ilike('email', emailValue)
          .limit(1),
        supabase
          .from('telegram_users')
          .select('email, temp_email')
          .or(`email.ilike.${emailValue},temp_email.ilike.${emailValue}`)
          .limit(1)
      ])

      if (usersResult.error || telegramUsersResult.error) {
        console.error('❌ Error checking tables (email):', { users: usersResult.error, telegram: telegramUsersResult.error })
        // Fallback to server-side duplicate check (service role)
        try {
          const resp = await fetch('/api/check-duplicates', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email: emailValue })
          })
          const dup = await resp.json()
          if (resp.ok && dup.emailDuplicate) {
            setErrors(prev => ({ ...prev, email: 'This email address is already registered. Please use a different email or try logging in.' }))
          }
        } catch (e) {
          console.warn('⚠️ Email duplicate server fallback failed')
        }
        return
      }

      console.log('📊 Users table results:', usersResult.data)
      console.log('📊 Telegram users table results:', telegramUsersResult.data)

      const foundInUsers = (usersResult.data && usersResult.data.length > 0) || false
      const foundInTelegram = (telegramUsersResult.data && telegramUsersResult.data.length > 0) || false

      if (foundInUsers || foundInTelegram) {
        console.log('❌ DUPLICATE EMAIL FOUND!')
        const tableFound = foundInUsers ? 'users' : 'telegram_users'
        console.log(`   Found in ${tableFound} table`)
        setErrors(prev => ({
          ...prev,
          email: 'This email address is already registered. Please use a different email or try logging in.'
        }))
      } else {
        console.log('✅ Email is available in both tables')
        // Clear email error if no duplicate found
        setErrors(prev => {
          const newErrors = { ...prev }
          if (newErrors.email && (newErrors.email.includes('already registered') || newErrors.email.includes('valid email'))) {
            delete newErrors.email
          }
          return newErrors
        })
      }
    } catch (error) {
      console.error('❌ Error checking duplicate email:', error)
    }
  }, [])

  // Debounced username duplicate check
  const checkUsernameDuplicate = useCallback(async (username: string) => {
    try {
      const uname = username.trim()
      console.log('🔍 Checking for duplicate username in both users and telegram_users tables:', uname)

      // Check both users and telegram_users tables (case-insensitive)
      const [usersResult, telegramUsersResult] = await Promise.all([
        supabase
          .from('users')
          .select('username')
          .ilike('username', uname)
          .limit(1),
        supabase
          .from('telegram_users')
          .select('username')
          .ilike('username', uname)
          .limit(1)
      ])

      if (usersResult.error || telegramUsersResult.error) {
        console.error('❌ Error checking tables (username):', { users: usersResult.error, telegram: telegramUsersResult.error })
        // Fallback to server-side duplicate check
        try {
          const resp = await fetch('/api/check-duplicates', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username: uname })
          })
          const dup = await resp.json()
          if (resp.ok && dup.usernameDuplicate) {
            setErrors(prev => ({ ...prev, username: 'This username is already taken. Please choose a different username.' }))
          }
        } catch (e) {
          console.warn('⚠️ Username duplicate server fallback failed')
        }
        return
      }

      console.log('📊 Users table results:', usersResult.data)
      console.log('📊 Telegram users table results:', telegramUsersResult.data)

      const foundInUsers = usersResult.data && usersResult.data.length > 0
      const foundInTelegram = telegramUsersResult.data && telegramUsersResult.data.length > 0

      if (foundInUsers || foundInTelegram) {
        console.log('❌ DUPLICATE USERNAME FOUND!')
        const tableFound = foundInUsers ? 'users' : 'telegram_users'
        console.log(`   Found in ${tableFound} table`)
        setErrors(prev => ({
          ...prev,
          username: 'This username is already taken. Please choose a different username.'
        }))
      } else {
        console.log('✅ Username is available in both tables')
        // Clear username error if no duplicate found
        setErrors(prev => {
          const newErrors = { ...prev }
          if (newErrors.username && (newErrors.username.includes('already taken') || newErrors.username.includes('at least 3 characters'))) {
            delete newErrors.username
          }
          return newErrors
        })
      }
    } catch (error) {
      console.error('❌ Error checking duplicate username:', error)
    }
  }, [])

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (emailCheckTimeoutRef.current) {
        clearTimeout(emailCheckTimeoutRef.current)
      }
      if (usernameCheckTimeoutRef.current) {
        clearTimeout(usernameCheckTimeoutRef.current)
      }
    }
  }, [])

  // Process URL parameters for referral links with campaign tracking
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const referralCode = urlParams.get('ref')
    const campaignSource = urlParams.get('campaign')

    if (referralCode) {
      console.log('🔗 Referral code detected:', referralCode)
      if (campaignSource) {
        console.log('📊 Campaign source detected:', campaignSource)
      }

      setFormData(prev => ({
        ...prev,
        sponsorUsername: referralCode,
        campaignSource: campaignSource || ''
      }))

      // Track the referral click for analytics
      trackReferralClick(referralCode, campaignSource, {
        userAgent: navigator.userAgent,
        refererUrl: document.referrer
      }).catch(error => {
        console.warn('⚠️ Failed to track referral click:', error)
      })
    }
  }, [])
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const verifyTelegramId = async (telegramId: string) => {
    if (!telegramId || !/^\d{8,12}$/.test(telegramId)) {
      setTelegramVerification(prev => ({
        ...prev,
        error: 'Please enter a valid Telegram ID (8-12 digits)',
        verified: false,
        telegramUser: null
      }))
      return
    }

    setTelegramVerification(prev => ({ ...prev, loading: true, error: '' }))

    try {
      const telegramIdNum = parseInt(telegramId)

      // Check if Telegram ID exists in telegram_users table
      // First, let's try with basic fields that should exist
      const { data: telegramUser, error: findError } = await supabase
        .from('telegram_users')
        .select('telegram_id, username, first_name, last_name')
        .eq('telegram_id', telegramIdNum)
        .single()

      if (findError) {
        console.error('Telegram user lookup error:', findError)
        setTelegramVerification(prev => ({
          ...prev,
          loading: false,
          error: 'Telegram ID not found. Please use our Telegram bot first (@AureusAllianceBot) to create your account.',
          verified: false,
          telegramUser: null
        }))
        return
      }

      if (!telegramUser) {
        setTelegramVerification(prev => ({
          ...prev,
          loading: false,
          error: 'Telegram ID not found. Please use our Telegram bot first (@AureusAllianceBot) to create your account.',
          verified: false,
          telegramUser: null
        }))
        return
      }

      // Check if already linked to web (if these columns exist)
      // For now, we'll assume if the user exists, they can be linked
      console.log('Found Telegram user:', telegramUser)

      // Fetch existing sponsor information
      let sponsorInfo = null
      try {
        // First get the user_id for this telegram user
        const { data: telegramUserData, error: telegramUserError } = await supabase
          .from('telegram_users')
          .select('user_id')
          .eq('telegram_id', telegramIdNum)
          .single()

        if (!telegramUserError && telegramUserData?.user_id) {
          // Now look for referral relationship using the user_id
          const { data: referralData, error: referralError } = await supabase
            .from('referrals')
            .select(`
              referrer_id,
              referral_code,
              status,
              created_at
            `)
            .eq('referred_id', telegramUserData.user_id)
            .eq('status', 'active')
            .single()

          if (!referralError && referralData) {
            // Get sponsor details from users table (referrer_id is always a users.id)
            const { data: sponsorUser, error: sponsorError } = await supabase
              .from('users')
              .select('id, username, full_name')
              .eq('id', referralData.referrer_id)
              .single()

            if (!sponsorError && sponsorUser) {
              sponsorInfo = {
                ...referralData,
                sponsor: sponsorUser
              }
            } else {
              console.warn('Could not find sponsor user details:', sponsorError)
            }
          } else {
            console.warn('No referral relationship found for user_id:', telegramUserData.user_id, referralError)
          }
        } else {
          console.warn('Could not find user_id for telegram_id:', telegramIdNum, telegramUserError)
        }
      } catch (error) {
        console.warn('Could not fetch sponsor information:', error)
        // Don't fail the verification for sponsor lookup errors
      }

      // Success - Telegram account found and available for linking
      setTelegramVerification({
        loading: false,
        error: '',
        verified: true,
        telegramUser,
        sponsorInfo
      })

      // Pre-fill form data with Telegram info
      setFormData(prev => ({
        ...prev,
        fullName: `${telegramUser.first_name} ${telegramUser.last_name || ''}`.trim(),
        username: telegramUser.username || `user_${telegramUser.telegram_id}`
      }))

    } catch (error) {
      console.error('Telegram verification error:', error)
      setTelegramVerification(prev => ({
        ...prev,
        loading: false,
        error: 'Error verifying Telegram ID. Please try again.',
        verified: false,
        telegramUser: null
      }))
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    const checked = (e.target as HTMLInputElement).checked

    console.log(`🔄 Input change: ${name} = "${value}" (registrationMode: ${registrationMode})`)

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))

    // Clear field-specific error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }

    // Handle email validation (for new registrations only)
    if (name === 'email' && registrationMode === 'new') {
      const emailValue = value.trim()
      const isValidEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue)

      console.log(`📧 Email validation: "${emailValue}" -> valid: ${isValidEmail}`)

      setEmailVerification(prev => ({
        ...prev,
        isEmailValid: isValidEmail,
        isVerified: false // Reset verification when email changes
      }))

      // Clear any existing timeout
      if (emailCheckTimeoutRef.current) {
        clearTimeout(emailCheckTimeoutRef.current)
      }

      // Check for duplicate email in real-time (debounced)
      if (isValidEmail) {
        console.log('📧 Scheduling email duplicate check...')
        setValidating(v => ({ ...v, email: true }))
        emailCheckTimeoutRef.current = setTimeout(async () => {
          await checkEmailDuplicate(emailValue)
          setValidating(v => ({ ...v, email: false }))
        }, 800) // Debounce for 800ms
      } else if (emailValue.length > 0) {
        // Show format error for invalid emails
        console.log('📧 Setting email format error')
        setErrors(prev => ({
          ...prev,
          email: 'Please enter a valid email address'
        }))
      }
    }

    // Handle username validation (for new registrations only)
    if (name === 'username' && registrationMode === 'new') {
      const usernameValue = value.trim()
      const isValidUsername = /^[a-zA-Z0-9_]+$/.test(usernameValue) && usernameValue.length >= 3

      console.log(`👤 Username validation: "${usernameValue}" -> valid: ${isValidUsername}`)

      // Clear any existing timeout
      if (usernameCheckTimeoutRef.current) {
        clearTimeout(usernameCheckTimeoutRef.current)
      }

      // Check for duplicate username in real-time (debounced)
      if (isValidUsername) {
        console.log('👤 Scheduling username duplicate check...')
        setValidating(v => ({ ...v, username: true }))
        usernameCheckTimeoutRef.current = setTimeout(async () => {
          await checkUsernameDuplicate(usernameValue)
          setValidating(v => ({ ...v, username: false }))
        }, 800) // Debounce for 800ms
      } else if (usernameValue.length > 0) {
        // Show format error for invalid usernames
        console.log('👤 Setting username format error')
        setErrors(prev => ({
          ...prev,
          username: 'Username must be at least 3 characters and contain only letters, numbers, and underscores'
        }))
      }
    }

    // Auto-verify Telegram ID when user finishes typing
    if (name === 'telegramId' && value.length >= 8) {
      const timeoutId = setTimeout(() => {
        verifyTelegramId(value)
      }, 500) // Debounce for 500ms

      return () => clearTimeout(timeoutId)
    }

    // Reset Telegram verification if user clears the field
    if (name === 'telegramId' && value.length < 8) {
      setTelegramVerification({
        verified: false,
        telegramUser: null,
        sponsorInfo: null,
        loading: false,
        error: ''
      })
    }
  }

  const handleEmailVerification = async (email: string) => {
    try {
      // Clear any previous errors
      setGeneralError('')
      console.log('🔍 Starting email verification process for:', email)

      // Check if there are any current validation errors
      if (errors.email || errors.username) {
        console.log('❌ Cannot proceed - validation errors exist:', { email: errors.email, username: errors.username })
        setGeneralError('Please fix the validation errors before proceeding.')
        return
      }

      // If still validating, block send
      if (validating.email || validating.username) {
        console.log('⏳ Still validating fields, please wait')
        return
      }
      // Force immediate validation check before proceeding
      console.log('🔍 Performing immediate validation check...')
      const emailValueRaw = email.trim()
      const usernameValueRaw = formData.username.trim()

      if (!emailValueRaw || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValueRaw)) {
        setErrors(prev => ({ ...prev, email: 'Please enter a valid email address' }))
        return
      }

      if (!usernameValueRaw || !/^[a-zA-Z0-9_]+$/.test(usernameValueRaw) || usernameValueRaw.length < 3) {
        setErrors(prev => ({ ...prev, username: 'Username must be at least 3 characters and contain only letters, numbers, and underscores' }))
        return
      }
      // Server-side duplicate check as a final gate (RLS-safe)
      try {
        const resp = await fetch('/api/check-duplicates', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email: emailValueRaw.toLowerCase(), username: usernameValueRaw.toLowerCase() })
        })
        const dup = await resp.json()
        if (resp.ok) {
          if (dup.emailDuplicate) {
            setErrors(prev => ({ ...prev, email: 'This email address is already registered. Please use a different email or try logging in.' }))
            return
          }
          if (dup.usernameDuplicate) {
            setErrors(prev => ({ ...prev, username: 'This username is already taken. Please choose a different username.' }))
            return
          }
        } else {
          console.warn('⚠️ Duplicate API error, falling back to client checks:', dup)
        }
      } catch (e) {
        console.warn('⚠️ Duplicate API request failed, proceeding with local validation only')
      }


      // First, check for duplicate email and username in both tables (case-insensitive, and checking both email/temp_email)
      console.log('🔍 Checking for duplicate email and username in both users and telegram_users tables...')

      const emailValueLower = email.trim().toLowerCase()
      const usernameValueLower = formData.username.trim().toLowerCase()

      const [usersResult, telegramUsersResult] = await Promise.all([
        supabase
          .from('users')
          .select('email, username')
          .or(`email.ilike.${emailValueLower},username.ilike.${usernameValueLower}`),
        supabase
          .from('telegram_users')
          .select('email, temp_email, username')
          .or(`email.ilike.${emailValueLower},temp_email.ilike.${emailValueLower},username.ilike.${usernameValueLower}`)
      ])

      if (usersResult.error) {
        console.error('❌ Error checking users table:', usersResult.error)
        setGeneralError('Failed to validate user information. Please try again.')
        return
      }

      if (telegramUsersResult.error) {
        console.error('❌ Error checking telegram_users table:', telegramUsersResult.error)
        setGeneralError('Failed to validate user information. Please try again.')
        return
      }

      console.log('🔍 Users table results:', usersResult.data)
      console.log('🔍 Telegram users table results:', telegramUsersResult.data)

      // Check for duplicates in users table
      if (usersResult.data && usersResult.data.length > 0) {
        const duplicateEmail = usersResult.data.find(user => (user.email || '').toLowerCase() === emailValue)
        const duplicateUsername = usersResult.data.find(user => (user.username || '').toLowerCase() === usernameValueLower)

        if (duplicateEmail) {
          console.log('❌ Duplicate email found in users table:', duplicateEmail)
          setErrors(prev => ({ ...prev, email: 'This email address is already registered. Please use a different email or try logging in.' }))
          return
        }

        if (duplicateUsername) {
          console.log('❌ Duplicate username found in users table:', duplicateUsername)
          setErrors(prev => ({ ...prev, username: 'This username is already taken. Please choose a different username.' }))
          return
        }
      }

      // Check for duplicates in telegram_users table
      if (telegramUsersResult.data && telegramUsersResult.data.length > 0) {
        const duplicateEmail = telegramUsersResult.data.find(user => ((user.temp_email || user.email || '').toLowerCase()) === emailValue)
        const duplicateUsername = telegramUsersResult.data.find(user => (user.username || '').toLowerCase() === usernameValueLower)

        if (duplicateEmail) {
          console.log('❌ Duplicate email found in telegram_users table:', duplicateEmail)
          setErrors(prev => ({ ...prev, email: 'This email address is already registered. Please use a different email or try logging in.' }))
          return
        }

        if (duplicateUsername) {
          console.log('❌ Duplicate username found in telegram_users table:', duplicateUsername)
          setErrors(prev => ({ ...prev, username: 'This username is already taken. Please choose a different username.' }))
          return
        }
      }

      console.log('✅ No duplicates found, proceeding with email verification')

      // Generate a 6-digit verification code
      const verificationCode = Math.floor(100000 + Math.random() * 900000).toString()

      setEmailVerification(prev => ({
        ...prev,
        tempUserId: null, // Not needed for this approach
        isModalOpen: true,
        verificationCode, // Store code for validation
        codeExpiry: Date.now() + (15 * 60 * 1000) // 15 minutes from now
      }))

      // Send verification email via server-side API
      console.log('📧 Attempting to send verification email via API...')
      const response = await fetch('/api/send-verification-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email,
          code: verificationCode,
          purpose: 'registration',
          userName: formData.fullName || 'User',
          expiryMinutes: 15
        }),
      })

      const responseData = await response.json()

      if (!response.ok) {
        console.error('❌ API Error:', responseData)
        setGeneralError(`Failed to send verification email: ${responseData.error || 'Server error'}`)
        return
      }

      console.log('✅ Verification email sent successfully via API:', responseData)
    } catch (error) {
      console.error('Email verification error:', error)
      setGeneralError(`Failed to send verification email: ${error instanceof Error ? error.message : 'Network error'}`)
    }
  }

  const handleEmailVerificationSuccess = () => {
    setEmailVerification(prev => ({
      ...prev,
      isVerified: true,
      isModalOpen: false,
      tempUserId: null,
      verificationCode: undefined,
      codeExpiry: undefined
    }))
  }

  const handleEmailVerificationClose = () => {
    setEmailVerification(prev => ({
      ...prev,
      isModalOpen: false,
      tempUserId: null,
      verificationCode: undefined,
      codeExpiry: undefined
    }))
  }

  const handleVerificationCodeSubmit = async (code: string) => {
    if (!emailVerification.verificationCode || !emailVerification.codeExpiry) {
      setGeneralError('No verification code found. Please request a new one.')
      return
    }

    if (Date.now() > emailVerification.codeExpiry) {
      setGeneralError('Verification code has expired. Please request a new one.')
      return
    }

    if (code === emailVerification.verificationCode) {
      handleEmailVerificationSuccess()
    } else {
      setGeneralError('Invalid verification code. Please try again.')
    }
  }

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {}

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format'
    } else if (registrationMode === 'new' && !emailVerification.isVerified) {
      newErrors.email = 'Please verify your email address first'
    }

    // Enhanced password validation using new security system
    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else {
      const passwordValidation = validatePasswordStrength(formData.password)
      if (!passwordValidation.valid) {
        newErrors.password = passwordValidation.errors[0] // Show first error
      }
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password'
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }

    // Full name validation
    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Full name is required'
    } else if (formData.fullName.trim().length < 2) {
      newErrors.fullName = 'Full name must be at least 2 characters'
    }

    // Username validation
    if (!formData.username.trim()) {
      newErrors.username = 'Username is required'
    } else if (formData.username.trim().length < 3) {
      newErrors.username = 'Username must be at least 3 characters'
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
      newErrors.username = 'Username can only contain letters, numbers, and underscores'
    }

    // Phone validation
    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required'
    } else if (!/^\+?[\d\s\-\(\)]{10,}$/.test(formData.phone.trim())) {
      newErrors.phone = 'Please enter a valid phone number'
    }

    // Country validation
    if (!formData.countryOfResidence) {
      newErrors.countryOfResidence = 'Country of residence is required'
    }

    // Telegram ID validation (only in telegram mode)
    if (registrationMode === 'telegram') {
      if (!formData.telegramId.trim()) {
        newErrors.telegramId = 'Telegram ID is required'
      } else if (!/^\d{8,12}$/.test(formData.telegramId)) {
        newErrors.telegramId = 'Telegram ID must be 8-12 digits'
      } else if (!telegramVerification.verified) {
        newErrors.telegramId = 'Please verify your Telegram ID first'
      }
    }

    // Sponsor username validation - only for new registrations, not Telegram linking
    if (registrationMode === 'new') {
      if (!formData.sponsorUsername.trim()) {
        newErrors.sponsorUsername = 'Sponsor username is required'
      } else if (formData.sponsorUsername.trim().length < 2) {
        newErrors.sponsorUsername = 'Sponsor username must be at least 2 characters'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setGeneralError('')

    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      if (registrationMode === 'telegram') {
        // Telegram account linking mode
        await handleTelegramAccountLinking()
      } else {
        // Standard registration mode
        const { user, error } = await registerUserWithEmail(formData)

        if (error) {
          setGeneralError(error.message)
        } else if (user) {
          console.log('✅ Registration successful:', user)
          onRegistrationSuccess(user)
        }
      }
    } catch (err) {
      console.error('Registration error:', err)
      setGeneralError('Registration failed. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleTelegramAccountLinking = async () => {
    if (!telegramVerification.verified || !telegramVerification.telegramUser) {
      throw new Error('Telegram account not verified')
    }

    try {
      console.log('🔄 Linking Telegram account to web access...')

      // Hash the password
      const passwordHash = await hashPassword(formData.password)

      // Update the telegram_users record with web access fields
      // Start with basic fields that should exist, then add others if they work
      const updateData: any = {
        is_registered: true,
        updated_at: new Date().toISOString()
      }

      // Try to add web-specific fields if they exist in the schema
      try {
        updateData.email = formData.email
        updateData.password_hash = passwordHash
        updateData.full_name = formData.fullName
        updateData.phone = formData.phone
        updateData.country_of_residence = formData.countryOfResidence
        updateData.is_web_enabled = true
        updateData.web_linked_at = new Date().toISOString()
        updateData.registration_step = 'web_linked'
      } catch (e) {
        console.warn('Some fields may not exist in telegram_users schema:', e)
      }

      const { data: updatedUser, error: updateError } = await supabase
        .from('telegram_users')
        .update(updateData)
        .eq('telegram_id', parseInt(formData.telegramId))
        .select()
        .single()

      if (updateError) {
        console.error('❌ Error updating telegram_users:', updateError)
        console.log('🔄 Falling back to standard registration with Telegram link...')

        // Fallback: Create a standard web user account and link it to Telegram
        const { user, error } = await registerUserWithEmail({
          ...formData,
          telegramId: formData.telegramId
        })

        if (error) {
          throw new Error('Failed to create linked account: ' + error.message)
        }

        if (user) {
          console.log('✅ Created web account with Telegram link')
          onRegistrationSuccess(user)
          return
        }
      }

      console.log('✅ Telegram account successfully updated for web access')

      // Note: Referral relationships already exist for Telegram users, no need to create new ones

      // Try to create Supabase auth user for login
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          data: {
            full_name: formData.fullName,
            username: formData.username,
            telegram_id: parseInt(formData.telegramId),
            is_telegram_linked: true
          }
        }
      })

      if (authError) {
        console.warn('⚠️ Telegram account updated but auth creation failed:', authError)
        setGeneralError('Account linked successfully! Please login with your email and password.')
        return
      }

      // Success - user is now registered and can login
      const userWithData = {
        ...authData.user,
        database_user: updatedUser,
        account_type: 'telegram_linked'
      }

      console.log('🎉 Telegram account linked and auth user created successfully')
      onRegistrationSuccess(userWithData)

    } catch (error) {
      console.error('❌ Telegram linking error:', error)
      throw error
    }
  }

  const createReferralRelationship = async (telegramId: number, sponsorUsername: string) => {
    try {
      // Find sponsor user (check both users and telegram_users tables)
      let sponsorId = null

      // First check users table
      const { data: sponsorUser } = await supabase
        .from('users')
        .select('id')
        .eq('username', sponsorUsername)
        .single()

      if (sponsorUser) {
        sponsorId = sponsorUser.id
      } else {
        // Check telegram_users table
        const { data: telegramSponsor } = await supabase
          .from('telegram_users')
          .select('telegram_id')
          .eq('username', sponsorUsername)
          .single()

        if (telegramSponsor) {
          sponsorId = telegramSponsor.telegram_id
        }
      }

      if (sponsorId) {
        // Create referral relationship
        await supabase
          .from('referrals')
          .insert({
            referrer_id: sponsorId,
            referred_id: telegramId,
            referral_code: `${sponsorUsername}_${telegramId}`,
            status: 'active'
          })

        console.log('✅ Referral relationship created')
      } else {
        console.warn('⚠️ Sponsor username not found:', sponsorUsername)
      }
    } catch (error) {
      console.error('❌ Error creating referral relationship:', error)
      // Don't fail the whole process for referral errors
    }
  }

  return (
    <div className="bg-gray-900/50 backdrop-blur-xl rounded-3xl p-8 border border-gray-700/30 shadow-2xl">
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center">
          <h3 className="text-2xl font-bold text-white mb-2">
            Create Your Account
          </h3>
          <p className="text-gray-400">
            Join Aureus Alliance Holdings and start your gold share ownership journey
          </p>
        </div>

        {/* Registration Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Telegram Integration Question */}
          <div className="bg-blue-900/20 border border-blue-500/30 rounded-xl p-6">
            <h4 className="text-lg font-semibold text-blue-300 mb-4">
              📱 Telegram Bot Integration
            </h4>
            <p className="text-gray-300 mb-4">
              Have you already used our Telegram bot (@AureusAllianceBot) to purchase shares or earn commissions?
            </p>
            <div className="space-y-3">
              <label className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name="registrationMode"
                  value="new"
                  checked={registrationMode === 'new'}
                  onChange={(e) => setRegistrationMode(e.target.value as 'new' | 'telegram')}
                  className="w-4 h-4 text-blue-500 bg-gray-800 border-gray-600 focus:ring-blue-500"
                />
                <span className="text-white">No, I'm new to Aureus Alliance</span>
              </label>
              <label className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name="registrationMode"
                  value="telegram"
                  checked={registrationMode === 'telegram'}
                  onChange={(e) => setRegistrationMode(e.target.value as 'new' | 'telegram')}
                  className="w-4 h-4 text-blue-500 bg-gray-800 border-gray-600 focus:ring-blue-500"
                />
                <span className="text-white">Yes, I've used the Telegram bot before</span>
              </label>
            </div>
            {registrationMode === 'telegram' && (
              <div className="mt-4 p-4 bg-green-900/20 border border-green-500/30 rounded-lg">
                <p className="text-green-300 text-sm">
                  ✅ Great! We'll link your existing Telegram bot data to your new web account.
                </p>
              </div>
            )}
          </div>

          {/* Telegram ID Field (only shown in telegram mode) */}
          {registrationMode === 'telegram' && (
            <div>
              <label htmlFor="telegramId" className="block text-sm font-semibold text-gray-300 mb-3">
                Telegram ID
              </label>
              <div className="space-y-2">
                <input
                  id="telegramId"
                  name="telegramId"
                  type="text"
                  value={formData.telegramId}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                    errors.telegramId ? 'border-red-500/50' :
                    telegramVerification.verified ? 'border-green-500/50' : 'border-gray-600/50'
                  }`}
                  placeholder="Example: 1393852532"
                />
                <p className="text-sm text-gray-400">
                  To find your Telegram ID: Open @AureusAllianceBot → Main Menu → Connect to Web → Your ID will be displayed
                </p>

                {/* Telegram Verification Status */}
                {telegramVerification.loading && (
                  <div className="flex items-center space-x-2 text-blue-400">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-400"></div>
                    <span className="text-sm">Verifying Telegram ID...</span>
                  </div>
                )}

                {telegramVerification.error && (
                  <p className="text-sm text-red-400">{telegramVerification.error}</p>
                )}

                {telegramVerification.verified && telegramVerification.telegramUser && (
                  <div className="p-3 bg-green-900/20 border border-green-500/30 rounded-lg">
                    <div className="flex items-center space-x-2 text-green-300 mb-2">
                      <span className="text-lg">✅</span>
                      <span className="font-semibold">Telegram Account Verified</span>
                    </div>
                    <p className="text-sm text-gray-300">
                      Found: <strong>{telegramVerification.telegramUser.first_name}</strong>
                      {telegramVerification.telegramUser.username && (
                        <span> (@{telegramVerification.telegramUser.username})</span>
                      )}
                    </p>
                    {telegramVerification.sponsorInfo && (
                      <p className="text-sm text-gray-300 mt-2">
                        Sponsor: <strong>{telegramVerification.sponsorInfo.sponsor.username}</strong>
                        <span className="text-xs text-gray-400 block">
                          Your existing sponsor relationship will be preserved
                        </span>
                      </p>
                    )}
                    <p className="text-xs text-gray-400 mt-1">
                      Your existing bot data will be linked to this web account
                    </p>
                  </div>
                )}

                {errors.telegramId && (
                  <p className="text-sm text-red-400">{errors.telegramId}</p>
                )}
              </div>
            </div>
          )}

          {/* Full Name */}
          <div>
            <label htmlFor="fullName" className="block text-sm font-semibold text-gray-300 mb-3">
              Full Name
            </label>
            <input
              id="fullName"
              name="fullName"
              type="text"
              value={formData.fullName}
              onChange={handleInputChange}
              autoComplete="name"
              className={`w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                errors.fullName ? 'border-red-500/50' : 'border-gray-600/50'
              }`}
              placeholder="Enter your full name"
            />
            {errors.fullName && (
              <p className="mt-2 text-sm text-red-400">{errors.fullName}</p>
            )}
          </div>

          {/* Username */}
          <div>
            <label htmlFor="username" className="block text-sm font-semibold text-gray-300 mb-3">
              Username
            </label>
            <input
              id="username"
              name="username"
              type="text"
              value={formData.username}
              onChange={handleInputChange}
              autoComplete="username"
              className={`w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                errors.username ? 'border-red-500/50' : 'border-gray-600/50'
              }`}
              placeholder="Choose a unique username"
            />
            {errors.username && (
              <p className="mt-2 text-sm text-red-400">{errors.username}</p>
            )}
          </div>

          {/* Email */}
          <div>
              {validating.email && (
                <span className="ml-2 text-xs text-gray-400">Checking…</span>
              )}

            <label htmlFor="email" className="block text-sm font-semibold text-gray-300 mb-3">
              Email Address
            </label>
            <div className="relative">
              <input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                autoComplete="email"
                className={`w-full px-4 py-4 pr-12 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                  errors.email ? 'border-red-500/50' :
                  emailVerification.isVerified ? 'border-green-500/50' : 'border-gray-600/50'
                }`}
                placeholder="<EMAIL>"
              />
              {registrationMode === 'new' && emailVerification.isEmailValid && (
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  {emailVerification.isVerified ? (
                    <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <button
                      type="button"
                      onClick={() => {
                        console.log('🔘 Send Code button clicked')
                        console.log('📊 Current state:', {
                          email: formData.email,
                          username: formData.username,
                          isEmailValid: emailVerification.isEmailValid,
                          emailError: errors.email,
                          usernameError: errors.username
                        })
                        handleEmailVerification(formData.email)
                      }}
                      className="bg-yellow-600 hover:bg-yellow-500 disabled:bg-gray-600 disabled:cursor-not-allowed text-white text-xs font-medium px-3 py-2 rounded-lg transition-colors duration-200 shadow-sm"
                      disabled={!emailVerification.isEmailValid || !!errors.email || !!errors.username || validating.email || validating.username}
                      title={validating.email || validating.username ? 'Validating...' : undefined}
                    >
                      {validating.email || validating.username ? 'Checking…' : 'Send Code'}
                    </button>
                  )}
                </div>
              )}
            </div>
            {errors.email && (
              <p className="mt-2 text-sm text-red-400">{errors.email}</p>
            )}
            {registrationMode === 'new' && emailVerification.isEmailValid && !emailVerification.isVerified && (
              <p className="mt-2 text-sm text-yellow-400">
                📧 Click "Send Code" to receive a 6-digit verification code via email
              </p>
            )}
            {registrationMode === 'new' && emailVerification.isVerified && (
              <p className="mt-2 text-sm text-green-400">
                ✅ Email verified successfully
              </p>
            )}
          </div>

          {/* Phone Number */}
          <div>
            <label htmlFor="phone" className="block text-sm font-semibold text-gray-300 mb-3">
              Phone Number
            </label>
            <input
              id="phone"
              name="phone"
              type="tel"
              value={formData.phone}
              onChange={handleInputChange}
              autoComplete="tel"
              className={`w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                errors.phone ? 'border-red-500/50' : 'border-gray-600/50'
              }`}
              placeholder="+27 12 345 6789"
            />
            {errors.phone && (
              <p className="mt-2 text-sm text-red-400">{errors.phone}</p>
            )}
          </div>

          {/* Country of Residence */}
          <div>
            <label htmlFor="countryOfResidence" className="block text-sm font-semibold text-gray-300 mb-3">
              Country of Residence
            </label>
            <select
              id="countryOfResidence"
              name="countryOfResidence"
              value={formData.countryOfResidence}
              onChange={handleInputChange}
              className={`w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white text-lg backdrop-blur-sm transition-all duration-200 ${
                errors.countryOfResidence ? 'border-red-500/50' : 'border-gray-600/50'
              }`}
            >
              <option value="ZAF">South Africa</option>
              <option value="USA">United States</option>
              <option value="GBR">United Kingdom</option>
              <option value="CAN">Canada</option>
              <option value="AUS">Australia</option>
              <option value="DEU">Germany</option>
              <option value="FRA">France</option>
              <option value="NLD">Netherlands</option>
              <option value="CHE">Switzerland</option>
              <option value="OTHER">Other</option>
            </select>
            {errors.countryOfResidence && (
              <p className="mt-2 text-sm text-red-400">{errors.countryOfResidence}</p>
            )}
          </div>

          {/* Telegram Account Question - Only for new users */}
          {registrationMode === 'new' && (
            <div>
              <label className="block text-sm font-semibold text-gray-300 mb-3">
                Do you have a Telegram account? (Optional)
              </label>
              <p className="text-sm text-gray-400 mb-3">
                If you have Telegram, you can connect it later for additional features.
              </p>
              <div className="flex items-center space-x-4 mb-4">
                <label className="flex items-center cursor-pointer">
                  <input
                    type="radio"
                    name="hasTelegramAccount"
                    value="true"
                    checked={formData.hasTelegramAccount === true}
                    onChange={() => setFormData(prev => ({ ...prev, hasTelegramAccount: true }))}
                    className="w-4 h-4 text-yellow-500 bg-gray-800 border-gray-600 focus:ring-yellow-500 focus:ring-2"
                  />
                  <span className="ml-2 text-white">Yes</span>
                </label>
                <label className="flex items-center cursor-pointer">
                  <input
                    type="radio"
                    name="hasTelegramAccount"
                    value="false"
                    checked={formData.hasTelegramAccount === false}
                    onChange={() => setFormData(prev => ({ ...prev, hasTelegramAccount: false, telegramUsername: '' }))}
                    className="w-4 h-4 text-yellow-500 bg-gray-800 border-gray-600 focus:ring-yellow-500 focus:ring-2"
                  />
                  <span className="ml-2 text-white">No</span>
                </label>
              </div>

              {/* Telegram Username - Only show if user has Telegram */}
              {formData.hasTelegramAccount && (
                <div>
                  <label htmlFor="telegramUsername" className="block text-sm font-semibold text-gray-300 mb-3">
                    Telegram Username
                  </label>
                  <input
                    id="telegramUsername"
                    name="telegramUsername"
                    type="text"
                    value={formData.telegramUsername}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                      errors.telegramUsername ? 'border-red-500/50' : 'border-gray-600/50'
                    }`}
                    placeholder="@yourusername (without @)"
                  />
                  {errors.telegramUsername && (
                    <p className="mt-2 text-sm text-red-400">{errors.telegramUsername}</p>
                  )}
                  <p className="mt-2 text-sm text-gray-500">
                    Enter your Telegram username without the @ symbol for future connection
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Password */}
          <div>
            <label htmlFor="password" className="block text-sm font-semibold text-gray-300 mb-3">
              Password
            </label>
            <div className="relative">
              <input
                id="password"
                name="password"
                type={showPassword ? "text" : "password"}
                value={formData.password}
                onChange={handleInputChange}
                autoComplete="new-password"
                className={`w-full px-4 py-4 pr-12 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                  errors.password ? 'border-red-500/50' : 'border-gray-600/50'
                }`}
                placeholder="••••••••"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
              >
                {showPassword ? (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                )}
              </button>
            </div>
            {errors.password && (
              <p className="mt-2 text-sm text-red-400">{errors.password}</p>
            )}
          </div>

          {/* Confirm Password */}
          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-semibold text-gray-300 mb-3">
              Confirm Password
            </label>
            <div className="relative">
              <input
                id="confirmPassword"
                name="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                value={formData.confirmPassword}
                onChange={handleInputChange}
                autoComplete="new-password"
                className={`w-full px-4 py-4 pr-12 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                  errors.confirmPassword ? 'border-red-500/50' : 'border-gray-600/50'
                }`}
                placeholder="••••••••"
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
              >
                {showConfirmPassword ? (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                )}
              </button>
            </div>
            {errors.confirmPassword && (
              <p className="mt-2 text-sm text-red-400">{errors.confirmPassword}</p>
            )}
          </div>

          {/* Sponsor Username - Only for new registrations */}
          {registrationMode === 'new' && (
            <div>
              <label htmlFor="sponsorUsername" className="block text-sm font-semibold text-gray-300 mb-3">
                Sponsor Username
              </label>
              <input
                id="sponsorUsername"
                name="sponsorUsername"
                type="text"
                value={formData.sponsorUsername}
                onChange={handleInputChange}
                className={`w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                  errors.sponsorUsername ? 'border-red-500/50' : 'border-gray-600/50'
                }`}
                placeholder="Enter your sponsor's username"
              />
              {errors.sponsorUsername && (
                <p className="mt-2 text-sm text-red-400">{errors.sponsorUsername}</p>
              )}
              <p className="mt-2 text-sm text-gray-500">
                This links your account to your sponsor for affiliate tracking
              </p>
            </div>
          )}

          {/* Existing Sponsor Information - For Telegram users */}
          {registrationMode === 'telegram' && telegramVerification.verified && (
            <div>
              <label className="block text-sm font-semibold text-gray-300 mb-3">
                Existing Sponsor Relationship
              </label>
              <div className="w-full px-4 py-4 bg-gray-800/40 border border-gray-600/50 rounded-xl text-gray-300 backdrop-blur-sm">
                {telegramVerification.sponsorInfo ? (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="text-green-400">✓</span>
                      <span className="font-medium">
                        Sponsor: {telegramVerification.sponsorInfo.sponsor.username}
                      </span>
                    </div>
                    <div className="text-sm text-gray-400">
                      {telegramVerification.sponsorInfo.sponsor.full_name}
                    </div>
                    <div className="text-xs text-gray-500">
                      Relationship established: {new Date(telegramVerification.sponsorInfo.created_at).toLocaleDateString()}
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 text-gray-400">
                    <span>ℹ️</span>
                    <span>No existing sponsor relationship found</span>
                  </div>
                )}
              </div>
              <p className="mt-2 text-sm text-gray-500">
                Your existing sponsor relationship from Telegram will be preserved
              </p>
            </div>
          )}

          {/* General Error */}
          {generalError && (
            <div className="p-4 bg-red-900/30 border border-red-500/50 rounded-xl text-red-300 text-sm backdrop-blur-sm">
              <div className="flex items-center gap-2">
                <span className="text-red-400">⚠️</span>
                {generalError}
              </div>
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={loading}
            className="w-full py-4 px-6 bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 font-bold rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-lg shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
            style={{ color: '#000000 !important' }}
          >
            {loading ? (
              <div className="flex items-center justify-center gap-2" style={{ color: '#000000 !important' }}>
                <div className="w-5 h-5 border-2 border-black/30 border-t-black rounded-full animate-spin"></div>
                <span style={{ color: '#000000 !important' }}>
                  {registrationMode === 'telegram' ? 'Linking Account...' : 'Creating Account...'}
                </span>
              </div>
            ) : (
              <span style={{ color: '#000000 !important' }}>
                {registrationMode === 'telegram' ? 'Link Telegram Account' : 'Create Account'}
              </span>
            )}
          </button>
        </form>

        {/* Switch to Login */}
        <div className="text-center">
          <p className="text-gray-300 mb-4">Already have an account?</p>
          <button
            onClick={onSwitchToLogin}
            className="text-yellow-400 hover:text-yellow-300 font-semibold transition-colors duration-200"
            style={{ color: '#facc15 !important' }}
          >
            <span style={{ color: '#facc15 !important' }}>Sign in instead</span>
          </button>
        </div>

        {/* Additional Info for Telegram Mode */}
        {registrationMode === 'telegram' && (
          <div className="border-t border-gray-700/50 pt-6">
            <div className="bg-blue-900/20 border border-blue-500/30 rounded-xl p-4">
              <h4 className="text-blue-300 font-semibold mb-2">📱 What happens next?</h4>
              <ul className="text-sm text-gray-300 space-y-1">
                <li>• Your existing Telegram bot data will be linked to this web account</li>
                <li>• All your share purchases and commissions will be accessible via web</li>
                <li>• You'll be automatically logged in after successful linking</li>
                <li>• Your Telegram bot will continue to work as before</li>
              </ul>
            </div>
          </div>
        )}
      </div>

      {/* Simple Email Verification Modal */}
      {emailVerification.isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-xl p-6 max-w-md w-full mx-4">
            <h3 className="text-xl font-semibold text-white mb-4">
              📧 Verify Your Email Address
            </h3>
            <p className="text-gray-300 mb-6">
              We've sent a 6-digit verification code to <strong>{formData.email}</strong>.
              Please enter it below to continue with your registration.
            </p>

            {/* Show general error if any */}
            {generalError && (
              <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-lg">
                <p className="text-red-400 text-sm">{generalError}</p>
              </div>
            )}

            <VerificationCodeInput
              onCodeSubmit={handleVerificationCodeSubmit}
              onClose={handleEmailVerificationClose}
              onResendCode={() => handleEmailVerification(formData.email)}
            />
          </div>
        </div>
      )}
    </div>
  )
}

// Simple verification code input component
const VerificationCodeInput: React.FC<{
  onCodeSubmit: (code: string) => void
  onClose: () => void
  onResendCode: () => void
}> = ({ onCodeSubmit, onClose, onResendCode }) => {
  const [code, setCode] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (code.length !== 6) {
      return
    }

    setIsSubmitting(true)
    onCodeSubmit(code)
    setIsSubmitting(false)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6)
    setCode(value)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Verification Code
        </label>
        <input
          type="text"
          value={code}
          onChange={handleInputChange}
          placeholder="Enter 6-digit code"
          className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white text-center text-lg tracking-widest focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
          maxLength={6}
          autoComplete="off"
        />
      </div>

      <div className="flex space-x-2">
        <button
          type="button"
          onClick={onClose}
          className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={onResendCode}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm"
        >
          Resend
        </button>
        <button
          type="submit"
          disabled={code.length !== 6 || isSubmitting}
          className="flex-1 px-4 py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
        >
          {isSubmitting ? 'Verifying...' : 'Verify'}
        </button>
      </div>
    </form>
  )
}
