import React, { useState, useEffect } from 'react'
import { signOut, getCurrentUser, supabase, getServiceRoleClient } from '../lib/supabase'
import SharePurchaseFlow from './SharePurchaseFlow'

// Note: Using main supabase client instead of creating additional instances
// to avoid "Multiple GoTrueClient instances" warning
import TelegramConnectionModal from './TelegramConnectionModal'
import { MarketingToolkit } from './MarketingToolkit'
import { EXPANSION_PLAN, EXPANSION_YEARS, getExpansionPlanForYear } from '../constants'
import { NotificationCenter } from './user/NotificationCenter'
import { NotificationBadge, NotificationDropdown } from './user/NotificationBadge'
import { ReferralCenter } from './referrals/ReferralCenter'
import { ComprehensiveDividendsCalculator } from './dividends/ComprehensiveDividendsCalculator'
import { ComprehensivePortfolio } from './portfolio/ComprehensivePortfolio'

interface UserDashboardProps {
  onLogout: () => void
  user?: any
  onNavigate?: (section: string) => void
}

// Enhanced Navigation Types
type DashboardSection =
  | 'overview'
  | 'purchase-shares'
  | 'portfolio'
  | 'referrals'
  | 'payments'
  | 'notifications'
  | 'company-presentation'
  | 'mining-operations'
  | 'community-relations'
  | 'support-center'
  | 'settings'
  | 'kyc'
  | 'legal-documents'

// Modern Icons - Enhanced Set
const DashboardIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
  </svg>
)

const SharesIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 5a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H7z" clipRule="evenodd"/>
  </svg>
)

const PortfolioIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
)

const CommissionIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
  </svg>
)

const ReferralIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
  </svg>
)

const PaymentIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
  </svg>
)

const NotificationIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM10.97 4.97a.235.235 0 0 0-.02 0 .327.327 0 0 0-.23.06.19.19 0 0 0-.06.1c0 .06.02.13.06.18l.02.02c.05.06.12.09.2.09s.15-.03.2-.09l.02-.02c.04-.05.06-.12.06-.18a.19.19 0 0 0-.06-.1.327.327 0 0 0-.23-.06.235.235 0 0 0-.02 0z" />
  </svg>
)

const PresentationIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 011 1v14a1 1 0 01-1 1H3a1 1 0 01-1-1V5a1 1 0 011-1h4z" />
  </svg>
)

const MiningIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
  </svg>
)

const CommunityIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
  </svg>
)

const SupportIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
  </svg>
)

const SettingsIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
)

const KYCIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
  </svg>
)

const LegalIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
  </svg>
)

// Navigation Menu Items Configuration
const navigationItems = [
  {
    id: 'overview' as DashboardSection,
    label: 'Dashboard Overview',
    icon: DashboardIcon,
    description: 'Your investment summary and quick actions'
  },
  {
    id: 'purchase-shares' as DashboardSection,
    label: 'Purchase Shares',
    icon: SharesIcon,
    description: 'Buy gold mining shares',
    highlight: true
  },
  {
    id: 'portfolio' as DashboardSection,
    label: 'My Portfolio',
    icon: PortfolioIcon,
    description: 'View your investments and performance'
  },
  {
    id: 'referrals' as DashboardSection,
    label: 'Referral Program',
    icon: ReferralIcon,
    description: 'Manage referrals and earn commissions'
  },
  {
    id: 'payments' as DashboardSection,
    label: 'Payment Status',
    icon: PaymentIcon,
    description: 'Track payments and transactions'
  },
  {
    id: 'notifications' as DashboardSection,
    label: 'Notifications',
    icon: NotificationIcon,
    description: 'View system updates and alerts',
    badge: 0 // Will be updated with actual count
  },
  {
    id: 'company-presentation' as DashboardSection,
    label: 'Company Presentation',
    icon: PresentationIcon,
    description: 'Learn about Aureus Alliance Holdings'
  },
  {
    id: 'mining-operations' as DashboardSection,
    label: 'Mining Operations',
    icon: MiningIcon,
    description: 'View mining progress and operations'
  },
  {
    id: 'community-relations' as DashboardSection,
    label: 'Community Relations',
    icon: CommunityIcon,
    description: 'Community development and engagement'
  },
  {
    id: 'support-center' as DashboardSection,
    label: 'Support Center',
    icon: SupportIcon,
    description: 'Get help and contact support'
  },
  {
    id: 'settings' as DashboardSection,
    label: 'Settings',
    icon: SettingsIcon,
    description: 'Manage your account preferences'
  },
  {
    id: 'kyc' as DashboardSection,
    label: 'KYC Verification',
    icon: KYCIcon,
    description: 'Complete identity verification',
    conditional: true // Only show if KYC not completed
  },
  {
    id: 'legal-documents' as DashboardSection,
    label: 'Legal Documents',
    icon: LegalIcon,
    description: 'Terms, privacy policy, and legal information'
  }
]

// Dividend Calculator Component
interface DividendCalculatorProps {
  userShares: number
}

const DividendCalculator: React.FC<DividendCalculatorProps> = ({ userShares }) => {
  // Initialize with 2026 expansion plan data
  const initialExpansionData = getExpansionPlanForYear(2026);
  const [inputs, setInputs] = useState({
    landHa: initialExpansionData?.hectares || 250, // Default to 2026 capacity
    avgGravelThickness: 0.8,
    inSituGrade: 0.9,
    recoveryFactor: 70,
    goldPriceUsdPerKg: 109026, // Current market price
    opexPercent: 45,
    dividendPayoutPercent: 50,
    selectedYear: 2026
  })

  const [calculated, setCalculated] = useState({
    numPlants: 0,
    annualRevenue: 0,
    annualEbit: 0,
    annualGoldKg: 0,
    dividendPerShare: 0,
    userAnnualDividend: 0
  })

  // Constants from the main calculator
  const PLANT_CAPACITY_TPH = 200
  const EFFECTIVE_HOURS_PER_DAY = 20
  const OPERATING_DAYS_PER_YEAR = 330
  const BULK_DENSITY_T_PER_M3 = 1.8
  const HA_PER_PLANT = 25
  const TOTAL_SHARES = 1400000

  useEffect(() => {
    const { landHa, avgGravelThickness, inSituGrade, recoveryFactor, goldPriceUsdPerKg, opexPercent } = inputs

    // Calculate based on user's land selection (using same formula as homepage calculator)
    const numPlants = landHa / HA_PER_PLANT
    const annualThroughputT = numPlants * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR
    const annualGoldKg = (annualThroughputT * (inSituGrade / BULK_DENSITY_T_PER_M3) * (recoveryFactor / 100)) / 1000
    const annualRevenue = annualGoldKg * goldPriceUsdPerKg
    const annualOperatingCost = annualRevenue * (opexPercent / 100)
    const annualEbit = annualRevenue - annualOperatingCost

    // Calculate dividend per share dynamically: Full EBIT ÷ Total Shares (100% payout like homepage)
    const dividendPerShare = TOTAL_SHARES > 0 ? annualEbit / TOTAL_SHARES : 0
    const userAnnualDividend = dividendPerShare * userShares

    setCalculated({
      numPlants,
      annualRevenue,
      annualEbit,
      annualGoldKg,
      dividendPerShare,
      userAnnualDividend
    })
  }, [inputs, userShares])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setInputs(prev => ({ ...prev, [name]: parseFloat(value) || 0 }))
  }

  // Handler for year selection - automatically sets corresponding hectares
  const handleYearSelection = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedYear = parseInt(e.target.value)
    const expansionData = getExpansionPlanForYear(selectedYear)

    console.log('Dashboard - Year selected:', selectedYear, 'Expansion data:', expansionData)

    if (expansionData) {
      setInputs(prev => ({
        ...prev,
        selectedYear: selectedYear,
        landHa: expansionData.hectares
      }))
    }
  }

  const formatNumber = (num: number, options?: Intl.NumberFormatOptions) => {
    return new Intl.NumberFormat('en-US', options).format(num)
  }

  const LAND_SIZE_OPTIONS = Array.from({ length: 200 }, (_, i) => (i + 1) * 25)

  return (
    <div style={{
      backgroundColor: 'rgba(31, 41, 55, 0.9)',
      borderRadius: '12px',
      padding: '24px',
      marginBottom: '30px',
      border: '1px solid #374151'
    }}>
      <h3 style={{
        color: '#F59E0B',
        fontSize: '20px',
        fontWeight: 'bold',
        margin: '0 0 8px 0',
        textAlign: 'center'
      }}>
        Dividend Calculator
      </h3>
      <p style={{
        color: '#9CA3AF',
        fontSize: '14px',
        textAlign: 'center',
        margin: '0 0 24px 0'
      }}>
        Configure your scenario and see how dividends are calculated based on your {userShares.toLocaleString()} shares
      </p>

      {/* Input Parameters */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
        gap: '20px',
        marginBottom: '24px'
      }}>
        <div style={{
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '8px',
          padding: '16px'
        }}>
          <h4 style={{ color: '#F59E0B', fontSize: '14px', fontWeight: 'bold', margin: '0 0 12px 0' }}>TARGET YEAR</h4>
          <select
            name="selectedYear"
            value={inputs.selectedYear}
            onChange={handleYearSelection}
            style={{
              width: '100%',
              padding: '8px 12px',
              backgroundColor: 'rgba(17, 24, 39, 0.8)',
              border: '1px solid #374151',
              borderRadius: '6px',
              color: 'white',
              fontSize: '16px'
            }}
          >
            {EXPANSION_YEARS.map(year => {
              const expansionData = getExpansionPlanForYear(year)
              return (
                <option key={year} value={year}>
                  {expansionData?.month} {year} - {expansionData?.plants} Plants ({expansionData?.hectares.toLocaleString()} ha)
                </option>
              )
            })}
          </select>
          <p style={{ color: '#9CA3AF', fontSize: '12px', margin: '8px 0 0 0' }}>
            Select our planned capacity for a specific year
          </p>
        </div>

        <div style={{
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '8px',
          padding: '16px'
        }}>
          <h4 style={{ color: '#F59E0B', fontSize: '14px', fontWeight: 'bold', margin: '0 0 12px 0' }}>LAND SIZE (Manual Override)</h4>
          <select
            name="landHa"
            value={inputs.landHa}
            onChange={handleInputChange}
            style={{
              width: '100%',
              padding: '8px 12px',
              backgroundColor: 'rgba(17, 24, 39, 0.8)',
              border: '1px solid #374151',
              borderRadius: '6px',
              color: 'white',
              fontSize: '16px'
            }}
          >
            {LAND_SIZE_OPTIONS.map(size => (
              <option key={size} value={size}>{size} ha</option>
            ))}
          </select>
          <p style={{ color: '#9CA3AF', fontSize: '12px', margin: '8px 0 0 0' }}>
            Corresponds to {formatNumber(inputs.landHa / HA_PER_PLANT * (TOTAL_SHARES / 10), { maximumFractionDigits: 0 })} shares<br/>
            Adjust manually for "what-if" scenarios
          </p>
        </div>

        <div style={{
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '8px',
          padding: '16px'
        }}>
          <h4 style={{ color: '#F59E0B', fontSize: '14px', fontWeight: 'bold', margin: '0 0 12px 0' }}>YOUR SHARES</h4>
          <div style={{
            padding: '8px 12px',
            backgroundColor: 'rgba(17, 24, 39, 0.4)',
            border: '1px solid #374151',
            borderRadius: '6px',
            color: '#9CA3AF',
            fontSize: '16px'
          }}>
            {userShares.toLocaleString()}
          </div>
          <p style={{ color: '#9CA3AF', fontSize: '12px', margin: '8px 0 0 0' }}>
            Total project shares: {TOTAL_SHARES.toLocaleString()}
          </p>
        </div>

        <div style={{
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '8px',
          padding: '16px'
        }}>
          <h4 style={{ color: '#F59E0B', fontSize: '14px', fontWeight: 'bold', margin: '0 0 12px 0' }}>GOLD PRICE</h4>
          <input
            type="number"
            name="goldPriceUsdPerKg"
            value={inputs.goldPriceUsdPerKg}
            onChange={handleInputChange}
            step="1000"
            style={{
              width: '100%',
              padding: '8px 12px',
              backgroundColor: 'rgba(17, 24, 39, 0.8)',
              border: '1px solid #374151',
              borderRadius: '6px',
              color: 'white',
              fontSize: '16px'
            }}
          />
          <p style={{ color: '#9CA3AF', fontSize: '12px', margin: '8px 0 0 0' }}>
            USD per kilogram
          </p>
        </div>
      </div>

      {/* Results */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '16px'
      }}>
        <div style={{
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '4px' }}>🏭</div>
          <div style={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}>
            {formatNumber(calculated.numPlants, { maximumFractionDigits: 1 })}
          </div>
          <div style={{ color: '#9CA3AF', fontSize: '12px' }}>Plants for Land</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '4px' }}>💰</div>
          <div style={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}>
            {formatNumber(calculated.annualRevenue, { style: 'currency', currency: 'USD', minimumFractionDigits: 0 })}
          </div>
          <div style={{ color: '#9CA3AF', fontSize: '12px' }}>Land Revenue Potential</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(139, 92, 246, 0.1)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '4px' }}>📊</div>
          <div style={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}>
            {formatNumber(calculated.annualEbit, { style: 'currency', currency: 'USD', minimumFractionDigits: 0 })}
          </div>
          <div style={{ color: '#9CA3AF', fontSize: '12px' }}>Land EBIT Potential</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(245, 158, 11, 0.1)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '4px' }}>🥇</div>
          <div style={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}>
            {formatNumber(calculated.annualGoldKg, { maximumFractionDigits: 0 })}
          </div>
          <div style={{ color: '#9CA3AF', fontSize: '12px' }}>kg/year</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(34, 197, 94, 0.1)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '4px' }}>💎</div>
          <div style={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}>
            {"$" + formatNumber(calculated.userAnnualDividend, { minimumFractionDigits: 2 })}
          </div>
          <div style={{ color: '#9CA3AF', fontSize: '12px' }}>Your Annual Dividend</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(168, 85, 247, 0.1)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '4px' }}>📈</div>
          <div style={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}>
            {"$" + formatNumber(calculated.dividendPerShare, { minimumFractionDigits: 4 })}
          </div>
          <div style={{ color: '#9CA3AF', fontSize: '12px' }}>Dividend Per Share</div>
        </div>
      </div>

      {/* Technical Parameters Display */}
      <div style={{
        marginTop: '24px',
        padding: '16px',
        backgroundColor: 'rgba(55, 65, 81, 0.3)',
        borderRadius: '8px',
        border: '1px solid #374151'
      }}>
        <h4 style={{ color: '#F59E0B', fontSize: '14px', fontWeight: 'bold', margin: '0 0 12px 0' }}>TECHNICAL PARAMETERS</h4>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '12px',
          fontSize: '13px'
        }}>
          <div>
            <span style={{ color: '#9CA3AF' }}>Gravel Thickness:</span>
            <span style={{ color: 'white', marginLeft: '8px' }}>{inputs.avgGravelThickness}m</span>
          </div>
          <div>
            <span style={{ color: '#9CA3AF' }}>In-situ Grade:</span>
            <span style={{ color: 'white', marginLeft: '8px' }}>{inputs.inSituGrade} g/t</span>
          </div>
          <div>
            <span style={{ color: '#9CA3AF' }}>Recovery Factor:</span>
            <span style={{ color: 'white', marginLeft: '8px' }}>{inputs.recoveryFactor}%</span>
          </div>
          <div>
            <span style={{ color: '#9CA3AF' }}>Operating Cost:</span>
            <span style={{ color: 'white', marginLeft: '8px' }}>{inputs.opexPercent}%</span>
          </div>
          <div>
            <span style={{ color: '#9CA3AF' }}>Dividend Payout:</span>
            <span style={{ color: 'white', marginLeft: '8px' }}>{inputs.dividendPayoutPercent}%</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export const UserDashboard: React.FC<UserDashboardProps> = ({ onLogout, user: propUser, onNavigate }) => {
  // Enhanced State Management
  const [user, setUser] = useState<any>(propUser || null)
  const [loading, setLoading] = useState(true)
  const [activeSection, setActiveSection] = useState<DashboardSection>('overview')
  const [showPurchaseFlow, setShowPurchaseFlow] = useState(false)
  const [showTelegramModal, setShowTelegramModal] = useState(false)
  const [showSharesModal, setShowSharesModal] = useState(false)
  const [telegramUser, setTelegramUser] = useState<any>(null)
  const [telegramLoading, setTelegramLoading] = useState(false)
  const [userSharePurchases, setUserSharePurchases] = useState<any[]>([])
  const [userData, setUserData] = useState<any>(null)
  const [commissionData, setCommissionData] = useState<any>(null)
  const [notificationCount, setNotificationCount] = useState(0)
  const [kycStatus, setKycStatus] = useState<'pending' | 'approved' | 'rejected' | 'not_started'>('not_started')
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  // Demo mode: DISABLED - All users have full access
  const isDemoUser = () => {
    return true // Always return true to give all users full access
  }

  // Helper function to get referral username
  const getReferralUsername = (user: any) => {
    // Priority order: username -> database_user.username -> email prefix -> fallback
    if (user?.username) {
      return user.username
    }
    if (user?.database_user?.username) {
      return user.database_user.username
    }
    if (user?.email) {
      const emailPrefix = user.email.split('@')[0]
      // Clean up email prefix to be URL-friendly
      return emailPrefix.replace(/[^a-zA-Z0-9]/g, '').toLowerCase()
    }
    if (user?.database_user?.email) {
      const emailPrefix = user.database_user.email.split('@')[0]
      return emailPrefix.replace(/[^a-zA-Z0-9]/g, '').toLowerCase()
    }
    // Final fallback using user ID
    const userId = user?.database_user?.id || user?.id
    return userId ? `user_${userId.toString().slice(-8)}` : 'user_unknown'
  }

  // Navigation Functions
  const handleSectionChange = (section: DashboardSection) => {
    setActiveSection(section)
    setIsMobileMenuOpen(false)

    // Handle special cases
    if (section === 'purchase-shares') {
      setShowPurchaseFlow(true)
    }
  }

  // Get filtered navigation items based on user status
  const getNavigationItems = () => {
    return navigationItems.filter(item => {
      if (item.conditional && item.id === 'kyc') {
        return kycStatus !== 'approved'
      }
      return true
    }).map(item => ({
      ...item,
      badge: item.id === 'notifications' ? notificationCount : item.badge
    }))
  }

  // Dashboard data states
  const [dashboardData, setDashboardData] = useState({
    totalShares: 0,
    shareValue: 0,
    futureDividends: 0,
    // Separate commission types like Telegram bot
    usdtCommissions: {
      totalEarned: 0,
      available: 0,
      escrowed: 0
    },
    shareCommissions: {
      totalShares: 0,
      currentValue: 0
    },
    accountBalance: 0,
    referralCount: 0,
    monthlyEarnings: 0,
    currentSharePrice: 25
  })
  const [dataLoading, setDataLoading] = useState(true)

  // Notification states
  const [showNotificationDropdown, setShowNotificationDropdown] = useState(false)

  useEffect(() => {
    loadUser()
  }, [])

  const loadUser = async () => {
    try {
      console.log('🔄 Loading current user...')

      // CRITICAL FIX: Try to get user from localStorage first
      let currentUser = null
      let userId = null

      try {
        const storedUser = localStorage.getItem('aureus_user')
        const storedTelegramUser = localStorage.getItem('aureus_telegram_user')
        const storedSession = localStorage.getItem('aureus_session')

        if (storedUser) {
          const parsedUser = JSON.parse(storedUser)

          // Create proper user object for email login
          currentUser = {
            id: parsedUser.auth_user_id || `db_${parsedUser.id}`,
            email: parsedUser.email,
            database_user: parsedUser,
            account_type: 'email',
            user_metadata: {
              telegram_id: parsedUser.telegram_id,
              username: parsedUser.username,
              full_name: parsedUser.full_name,
              phone: parsedUser.phone,
              country_of_residence: parsedUser.country_of_residence,
              user_id: parsedUser.id,
              is_email_user: true,
              telegram_connected: !!parsedUser.telegram_id
            }
          }
          userId = parsedUser.id
          console.log('✅ Email user loaded from localStorage:', currentUser, 'User ID:', userId, 'Type:', typeof userId)
          console.log('🔍 DEBUG: parsedUser structure:', {
            id: parsedUser.id,
            auth_user_id: parsedUser.auth_user_id,
            telegram_id: parsedUser.telegram_id,
            email: parsedUser.email
          })
        } else if (storedTelegramUser) {
          const parsedTelegramUser = JSON.parse(storedTelegramUser)
          currentUser = parsedTelegramUser
          userId = parsedTelegramUser.database_user?.id
          console.log('✅ User loaded from telegram localStorage:', currentUser, 'User ID:', userId)
        }
      } catch (storageError) {
        console.log('⚠️ Could not load from localStorage, trying getCurrentUser')
      }

      // Fallback to getCurrentUser if localStorage fails
      if (!currentUser) {
        currentUser = await getCurrentUser()
        // CRITICAL FIX: Extract the correct database user ID with priority order
        userId = currentUser?.database_user?.id ||
                 currentUser?.user_metadata?.user_id ||
                 (currentUser?.user_metadata?.telegram_id ? currentUser.user_metadata.telegram_id : null)

        console.log('👤 Current user loaded from getCurrentUser:', currentUser, 'User ID:', userId)
        console.log('🔍 DEBUG: User ID extraction:', {
          database_user_id: currentUser?.database_user?.id,
          user_metadata_user_id: currentUser?.user_metadata?.user_id,
          telegram_id: currentUser?.user_metadata?.telegram_id,
          final_userId: userId,
          userId_type: typeof userId
        })
      }

      setUser(currentUser)

      // IMMEDIATE FIX: Set user data from authenticated session to avoid database queries
      if (currentUser?.database_user || currentUser?.user_metadata) {
        const userData = {
          telegram_id: currentUser?.database_user?.telegram_id || currentUser?.user_metadata?.telegram_id,
          username: currentUser?.database_user?.username || currentUser?.user_metadata?.username || 'User',
          full_name: currentUser?.database_user?.full_name || currentUser?.user_metadata?.full_name || 'User'
        }
        console.log('✅ User data set from session:', userData)
        setUserData(userData)

        // Set default commission data to avoid 406 errors
        setCommissionData({
          usdt_balance: 0,
          share_balance: 0,
          total_earned_usdt: 0,
          total_earned_shares: 0,
          escrowed_amount: 0,
          total_withdrawn: 0
        })
        console.log('✅ Default commission data set')

        // Set default share purchases to avoid errors
        setUserSharePurchases([])
        console.log('✅ Default share purchases set')

        // CRITICAL FIX: Set user state and load dashboard data
        if (userId && currentUser) {
          console.log('🔄 Setting user state and loading dashboard data for user ID:', userId, 'Type:', typeof userId)

          // CRITICAL: Ensure userId is numeric before making database calls
          let numericUserId = null
          if (typeof userId === 'number' && !isNaN(userId)) {
            numericUserId = userId
          } else if (typeof userId === 'string') {
            const parsed = parseInt(userId, 10)
            if (!isNaN(parsed)) {
              numericUserId = parsed
            }
          }

          if (!numericUserId) {
            console.error('❌ Cannot convert user ID to numeric:', userId)
            return
          }

          console.log('✅ Using numeric user ID for database operations:', numericUserId)
          setUser(currentUser) // Set the user state so it displays in the UI
          await loadDashboardData(numericUserId)
          await checkTelegramConnection(numericUserId)
          await loadUserSharePurchases(numericUserId)
        } else {
          console.log('⚠️ No valid user ID found for data loading')
        }
      }
    } catch (error) {
      console.error('❌ Error loading user:', error)
    } finally {
      setLoading(false)
    }
  }

  // Load user share purchases
  const loadUserSharePurchases = async (userId: number) => {
    try {
      console.log('🔄 Loading share purchases for user:', userId)

      const serviceClient = getServiceRoleClient()
      const { data: purchases, error } = await serviceClient
        .from('aureus_share_purchases')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('❌ Error loading share purchases:', error)
        return
      }

      console.log('✅ Share purchases loaded:', purchases)
      setUserSharePurchases(purchases || [])
    } catch (error) {
      console.error('❌ Failed to load share purchases:', error)
    }
  }

  const loadDashboardData = async (userId: number) => {
    try {
      setDataLoading(true)
      console.log('🔄 Loading dashboard data for user:', userId, typeof userId)

      // Validate userId - handle both numeric and string IDs
      let validUserId = null
      if (typeof userId === 'number' && !isNaN(userId)) {
        validUserId = userId
      } else if (typeof userId === 'string') {
        const numericId = parseInt(userId, 10)
        if (!isNaN(numericId)) {
          validUserId = numericId
        }
      }

      if (!validUserId) {
        console.error('❌ Invalid user ID:', userId, 'Type:', typeof userId)
        // Set default data to prevent UI errors
        setCommissionData({
          usdt_balance: 0,
          share_balance: 0,
          total_earned_usdt: 0,
          total_earned_shares: 0,
          escrowed_amount: 0,
          total_withdrawn: 0
        })
        setUserSharePurchases([])
        setDataLoading(false)
        return
      }

      // Use the validated numeric user ID
      userId = validUserId
      console.log('✅ Using validated user ID:', userId, 'Type:', typeof userId)

      // Get service role client for database queries
      const serviceRoleClient = getServiceRoleClient()

      // Get current share price from active phase - use service role for this public data
      let currentSharePrice = 5.00 // Default fallback
      try {
        const { data: currentPhase, error: phaseError } = await supabase
          .from('investment_phases')
          .select('price_per_share')
          .eq('is_active', true)
          .single()

        if (phaseError) {
          console.log('⚠️ Could not fetch current phase, using default price:', phaseError.message)
        } else {
          currentSharePrice = currentPhase?.price_per_share || 5.00
        }
      } catch (error) {
        console.log('⚠️ Phase query failed, using default price:', error)
      }

      console.log('📊 Current share price:', currentSharePrice)

      // Fetch user shares from aureus_share_purchases - use service role client
      let sharesPurchases = []
      try {
        console.log('🔍 Fetching shares for user ID:', userId, typeof userId)
        const { data, error: sharesError } = await serviceRoleClient
          .from('aureus_share_purchases')
          .select('shares_purchased, total_amount, status')
          .eq('user_id', userId)
          .eq('status', 'active')

        if (sharesError) {
          console.log('⚠️ Could not fetch shares:', sharesError.message)
        } else {
          sharesPurchases = data || []
          console.log('✅ Shares data:', sharesPurchases)
        }
      } catch (error) {
        console.log('⚠️ Shares query failed:', error)
        sharesPurchases = []
      }

      // Fetch commission balance - use service role client to bypass RLS
      let commissionBalance = null
      try {
        console.log('🔍 Fetching commission balance for user ID:', userId, typeof userId)
        const { data, error: commissionError } = await serviceRoleClient
          .from('commission_balances')
          .select('usdt_balance, share_balance, total_earned_usdt, total_earned_shares, escrowed_amount, total_withdrawn')
          .eq('user_id', userId)
          .single()

        if (commissionError && commissionError.code !== 'PGRST116') {
          console.log('⚠️ Could not fetch commission balance:', commissionError.message)
        } else if (commissionError?.code === 'PGRST116') {
          console.log('ℹ️ No commission balance record found (expected for new users)')
        } else {
          commissionBalance = data
          console.log('✅ Commission balance data:', commissionBalance)
        }
      } catch (error) {
        console.log('⚠️ Commission balance query failed:', error)
        commissionBalance = null
      }

      // Fetch referral count
      let referrals = []
      try {
        console.log('🔍 Fetching referrals for user ID:', userId)
        const { data, error: referralsError } = await serviceRoleClient
          .from('referrals')
          .select('id')
          .eq('referrer_id', userId)

        if (referralsError) {
          console.log('⚠️ Could not fetch referrals:', referralsError.message)
        } else {
          referrals = data || []
          console.log('✅ Referrals data:', referrals)
        }
      } catch (error) {
        console.log('⚠️ Referrals query failed:', error)
        referrals = []
      }

      // Calculate purchased shares totals
      const purchasedShares = sharesPurchases?.reduce((sum, purchase) => sum + purchase.shares_purchased, 0) || 0
      const purchasedSharesValue = sharesPurchases?.reduce((sum, purchase) => sum + purchase.total_amount, 0) || 0

      // Commission data - separate USDT and Share commissions like Telegram bot
      const totalEarnedUSDT = commissionBalance?.total_earned_usdt || 0
      const totalEarnedShares = commissionBalance?.total_earned_shares || 0
      const availableUSDT = commissionBalance?.usdt_balance || 0
      const availableShares = commissionBalance?.share_balance || 0
      const escrowedAmount = commissionBalance?.escrowed_amount || 0

      // Calculate TOTAL shares owned (purchased + commission shares)
      const totalShares = purchasedShares + availableShares
      console.log('📊 Share calculation:', {
        purchasedShares,
        availableShares,
        totalShares,
        currentSharePrice
      })

      // Calculate TOTAL share value using current share price
      const shareValue = totalShares * currentSharePrice
      const shareCommissionValue = totalEarnedShares * currentSharePrice

      // Account balance = available USDT + available shares value
      const accountBalance = availableUSDT + (availableShares * currentSharePrice)

      const referralCount = referrals?.length || 0

      // Calculate future dividends based on total owned shares using dynamic EBIT calculation
      // Use the same calculation logic as the homepage calculator for consistency

      // Constants for calculation (same as homepage calculator)
      const PLANT_CAPACITY_TPH = 200
      const EFFECTIVE_HOURS_PER_DAY = 20
      const OPERATING_DAYS_PER_YEAR = 330
      const BULK_DENSITY_T_PER_M3 = 1.8
      const HA_PER_PLANT = 25
      const TOTAL_SHARES_CALC = 1400000

      // Default parameters for future dividend calculation (25ha baseline)
      const baselineLandHa = 25
      const avgGravelThickness = 0.8
      const inSituGrade = 0.9
      const recoveryFactor = 70
      const goldPriceUsdPerKg = 100000
      const opexPercent = 45

      // Calculate baseline EBIT for 25ha (1 plant) using same formula as homepage calculator
      const numPlants = baselineLandHa / HA_PER_PLANT
      const annualThroughputT = numPlants * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR
      const annualGoldKg = (annualThroughputT * (inSituGrade / BULK_DENSITY_T_PER_M3) * (recoveryFactor / 100)) / 1000
      const annualRevenue = annualGoldKg * goldPriceUsdPerKg
      const annualOperatingCost = annualRevenue * (opexPercent / 100)
      const baselineEbit = annualRevenue - annualOperatingCost

      // Calculate dividend per share dynamically: Full EBIT ÷ Total Shares (100% payout like homepage)
      const dividendPerShare = TOTAL_SHARES_CALC > 0 ? baselineEbit / TOTAL_SHARES_CALC : 0
      const futureDividends = totalShares * dividendPerShare

      setDashboardData({
        totalShares,
        shareValue,
        futureDividends,
        // Separate commission types like Telegram bot
        usdtCommissions: {
          totalEarned: totalEarnedUSDT,
          available: availableUSDT,
          escrowed: escrowedAmount
        },
        shareCommissions: {
          totalShares: totalEarnedShares,
          currentValue: shareCommissionValue
        },
        accountBalance,
        referralCount,
        monthlyEarnings: (totalEarnedUSDT + shareCommissionValue) / 12, // Rough monthly average
        currentSharePrice
      })

      console.log('✅ Dashboard data loaded:', {
        totalShares,
        shareValue,
        futureDividends,
        usdtCommissions: {
          totalEarned: totalEarnedUSDT,
          available: availableUSDT,
          escrowed: escrowedAmount
        },
        shareCommissions: {
          totalShares: totalEarnedShares,
          currentValue: shareCommissionValue
        },
        accountBalance,
        referralCount,
        currentSharePrice
      })

    } catch (error) {
      console.error('Error loading dashboard data:', error)
      // Set default data to prevent UI errors
      setCommissionData({
        usdt_balance: 0,
        share_balance: 0,
        total_earned_usdt: 0,
        total_earned_shares: 0,
        escrowed_amount: 0,
        total_withdrawn: 0
      })
      setUserSharePurchases([])
    } finally {
      setDataLoading(false)
    }
  }

  const checkTelegramConnection = async (userId: number) => {
    try {
      console.log('🔍 IMMEDIATE FIX: Setting Telegram connection from authenticated user data')

      // CRITICAL FIX: Check if user exists before accessing properties
      if (!user) {
        console.log('❌ User object is undefined, cannot check Telegram connection')
        setTelegramUser(null)
        return
      }

      console.log('🔍 DEBUG: User object structure:', {
        hasUser: !!user,
        userKeys: user ? Object.keys(user) : [],
        hasDatabaseUser: !!user?.database_user,
        hasUserMetadata: !!user?.user_metadata,
        userMetadataKeys: user?.user_metadata ? Object.keys(user.user_metadata) : []
      });

      // IMMEDIATE FIX: Use data from authenticated user instead of database queries
      // Handle different user object structures after authentication fix
      const telegramId = user?.database_user?.telegram_id ||
                        user?.user_metadata?.telegram_id ||
                        user?.telegram_id;

      if (telegramId) {
        console.log('✅ User has Telegram ID from session:', telegramId)

        setTelegramUser({
          id: telegramId,
          telegram_id: telegramId,
          username: user?.database_user?.username || user?.user_metadata?.username || user?.username || 'User',
          full_name: user?.database_user?.full_name || user?.user_metadata?.full_name || user?.full_name || 'User'
        })
        console.log('✅ Telegram user set from session data')
      } else {
        console.log('❌ No Telegram connection found in session')
        setTelegramUser(null)
      }
    } catch (error) {
      console.error('Error checking Telegram connection:', error)
    }
  }

  const handleLogout = async () => {
    await signOut()
    onLogout()
  }

  const handleTelegramConnect = async (telegramId: string) => {
    if (!user?.database_user?.id) {
      throw new Error('User not authenticated')
    }

    // Validate telegram_id format (should be numeric)
    if (!/^\d{8,12}$/.test(telegramId)) {
      throw new Error('Invalid Telegram ID format')
    }

    try {
      // Call the telegram-link API endpoint
      const response = await fetch('http://localhost:8003/api/telegram-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: user.database_user.id,
          telegram_id: telegramId
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to connect Telegram account');
      }

      // Success - refresh the page to update the UI
      alert('✅ Telegram account connected successfully! The page will refresh to update your connection status.');
      window.location.reload();

    } catch (error) {
      console.error('Telegram connection error:', error);
      throw error;
    }
  }

  const handleTelegramDisconnect = async () => {
    if (!user?.database_user?.id || !telegramUser) {
      return
    }

    try {
      // Unlink the accounts by removing the user_id from telegram_users record
      const { error: updateError } = await supabase
        .from('telegram_users')
        .update({
          user_id: null,
          is_registered: false,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.database_user.id)

      if (updateError) {
        console.error('Error unlinking telegram_users:', updateError)
        throw new Error('Failed to unlink telegram_users record')
      }

      // Also clear telegram_id from users table
      const { error: userUpdateError } = await supabase
        .from('users')
        .update({
          telegram_id: null,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.database_user.id)

      if (userUpdateError) {
        console.error('Error clearing users telegram_id:', userUpdateError)
        // Don't fail the whole process, but log the error
        console.warn('⚠️ Failed to clear telegram_id from users table, but telegram_users unlink was successful')
      }

      setTelegramUser(null)
      console.log('✅ Telegram account disconnected successfully')
    } catch (error) {
      console.error('❌ Telegram disconnect error:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[#0f1419]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading your dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-800 via-gray-900 to-gray-800 text-white">
      {/* Mobile Header - Visible only on mobile */}
      <div className="lg:hidden bg-gray-800/90 backdrop-blur-lg border-b border-gray-700 sticky top-0 z-50">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center">
              <span className="text-black font-bold text-sm">A</span>
            </div>
            <span className="font-bold text-lg">Aureus Alliance</span>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="p-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors"
            aria-label="Toggle menu"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {isMobileMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMobileMenuOpen && (
        <div className="lg:hidden fixed inset-0 z-40 bg-black/50" onClick={() => setIsMobileMenuOpen(false)}>
          <div className="fixed inset-y-0 left-0 w-80 bg-gray-800 shadow-xl" onClick={(e) => e.stopPropagation()}>
            <div className="flex flex-col h-full">
              {/* Mobile Menu Header */}
              <div className="p-6 border-b border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-xl flex items-center justify-center">
                      <span className="text-black font-bold text-lg">A</span>
                    </div>
                    <span className="text-xl font-bold text-white">Aureus Alliance</span>
                  </div>
                  <button
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="p-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Mobile Navigation */}
              <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
                {getNavigationItems().map((item) => {
                  const IconComponent = item.icon
                  const isActive = activeSection === item.id

                  return (
                    <div
                      key={item.id}
                      onClick={() => handleSectionChange(item.id)}
                      className={`
                        group relative p-3 rounded-lg cursor-pointer transition-all duration-200
                        ${isActive
                          ? 'bg-blue-600/20 border border-blue-500/30 text-blue-400'
                          : 'hover:bg-gray-700/50 text-gray-300 hover:text-white'
                        }
                        ${item.highlight ? 'ring-2 ring-yellow-500/30' : ''}
                      `}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`
                          p-2 rounded-lg transition-colors
                          ${isActive ? 'bg-blue-500/20' : 'bg-gray-700/50 group-hover:bg-gray-600/50'}
                        `}>
                          <IconComponent />
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-sm truncate">{item.label}</span>
                            {item.badge !== undefined && item.badge > 0 && (
                              <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full ml-2">
                                {item.badge}
                              </span>
                            )}
                          </div>
                          <p className="text-xs text-gray-400 mt-1 truncate">{item.description}</p>
                        </div>
                      </div>

                      {item.highlight && (
                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
                      )}
                    </div>
                  )
                })}
              </nav>

              {/* Mobile User Info */}
              <div className="p-4 border-t border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-medium">
                        {(user?.username || user?.email)?.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <p className="text-white text-sm font-medium">
                        {user?.username || user?.email?.split('@')[0]}
                      </p>
                      <p className="text-gray-400 text-xs">Active</p>
                    </div>
                  </div>
                  <button
                    onClick={onLogout}
                    className="p-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors"
                    title="Logout"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex min-h-screen lg:min-h-0">
        {/* Desktop Sidebar - Hidden on mobile */}
        <div className="hidden lg:flex lg:w-80 bg-gray-800/80 border-r border-gray-700 flex-col backdrop-blur-lg">
          {/* Desktop Logo */}
          <div className="p-6 border-b border-gray-700">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-xl flex items-center justify-center">
                <span className="text-black font-bold text-lg">A</span>
              </div>
              <span className="text-xl font-bold">Aureus Alliance</span>
            </div>
          </div>

        {/* Enhanced Navigation */}
        <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
          {getNavigationItems().map((item) => {
            const IconComponent = item.icon
            const isActive = activeSection === item.id

            return (
              <div
                key={item.id}
                onClick={() => handleSectionChange(item.id)}
                className={`
                  group relative p-3 rounded-lg cursor-pointer transition-all duration-200
                  ${isActive
                    ? 'bg-blue-600/20 border border-blue-500/30 text-blue-400'
                    : 'hover:bg-gray-700/50 text-gray-300 hover:text-white'
                  }
                  ${item.highlight ? 'ring-2 ring-yellow-500/30' : ''}
                `}
              >
                <div className="flex items-center gap-3">
                  <div className={`
                    p-2 rounded-lg transition-colors
                    ${isActive ? 'bg-blue-500/20' : 'bg-gray-700/50 group-hover:bg-gray-600/50'}
                  `}>
                    <IconComponent />
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-sm truncate">{item.label}</span>
                      {item.badge !== undefined && item.badge > 0 && (
                        <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full ml-2">
                          {item.badge}
                        </span>
                      )}
                    </div>
                    <p className="text-xs text-gray-400 mt-1 truncate">{item.description}</p>
                  </div>
                </div>

                {item.highlight && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
                )}
              </div>
            )
          })}
        </nav>

        {/* User Info at Bottom */}
        <div style={{ padding: '16px', borderTop: '1px solid #374151' }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div style={{
                width: '32px',
                height: '32px',
                background: 'linear-gradient(135deg, #60a5fa 0%, #a855f7 100%)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <span style={{ color: 'white', fontSize: '14px', fontWeight: '500' }}>
                  {(user?.username || user?.email)?.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <p style={{ color: 'white', fontSize: '14px', fontWeight: '500', margin: 0 }}>
                  {user?.username || user?.email?.split('@')[0]}
                </p>
                <p style={{ color: '#9ca3af', fontSize: '12px', margin: 0 }}>Active</p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              style={{
                background: 'none',
                border: 'none',
                color: '#9ca3af',
                cursor: 'pointer',
                padding: '4px'
              }}
              title="Logout"
            >
              <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div style={{ flex: 1, padding: '32px' }}>
        {/* Header */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '32px' }}>
          <div>
            <h1 style={{ fontSize: '32px', fontWeight: 'bold', color: 'white', margin: 0 }}>Dashboard</h1>
            <p style={{ color: '#9ca3af', marginTop: '8px', margin: 0 }}>
              Welcome back, {user?.username || user?.email?.split('@')[0]}!
            </p>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            {/* Notification Badge with Dropdown */}
            <div style={{ position: 'relative' }}>
              <NotificationBadge
                userId={user?.database_user?.id || user?.id}
                onClick={() => setShowNotificationDropdown(!showNotificationDropdown)}
                className="p-2 bg-gray-800 rounded-lg border border-gray-700 hover:bg-gray-700 transition-colors"
              />

              <NotificationDropdown
                userId={user?.database_user?.id || user?.id}
                isOpen={showNotificationDropdown}
                onClose={() => setShowNotificationDropdown(false)}
                onViewAll={() => {
                  setActiveSection('notifications')
                  setShowNotificationDropdown(false)
                }}
              />
            </div>

            <div style={{
              backgroundColor: 'rgba(31, 41, 55, 0.8)',
              backdropFilter: 'blur(10px)',
              padding: '16px 24px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <span style={{ color: '#9ca3af', fontSize: '14px', display: 'block' }}>Account Balance</span>
              <p style={{ color: 'white', fontWeight: 'bold', fontSize: '18px', margin: 0 }}>
                {"$" + (dataLoading ? '...' : dashboardData.accountBalance.toFixed(2))}
              </p>
            </div>

            {/* Prominent Logout Button */}
            <button
              onClick={handleLogout}
              style={{
                background: 'linear-gradient(135deg, #dc2626, #b91c1c)',
                border: 'none',
                color: 'white',
                padding: '12px 20px',
                borderRadius: '12px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '600',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                transition: 'all 0.3s ease',
                boxShadow: '0 4px 12px rgba(220, 38, 38, 0.3)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 6px 16px rgba(220, 38, 38, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(220, 38, 38, 0.3)';
              }}
              title="Sign out of your account"
            >
              <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              Logout
            </button>
          </div>
        </div>



        {/* Conditional Content Based on Active Section */}
        {activeSection === 'notifications' ? (
          <NotificationCenter
            userId={user?.database_user?.id || user?.id}
            className="bg-gray-800 rounded-lg border border-gray-700 p-6"
          />
        ) : activeSection === 'overview' ? (
          <>
            {/* Simple Overview */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '20px',
              marginBottom: '30px'
            }}>
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '12px',
            padding: '20px',
            textAlign: 'center',
            border: '1px solid #374151'
          }}>
            <p style={{ fontSize: '32px', fontWeight: 'bold', color: '#3b82f6', margin: '0 0 5px 0' }}>
              {dataLoading ? '...' : dashboardData.totalShares.toLocaleString()}
            </p>
            <p style={{ fontSize: '14px', color: '#9ca3af', margin: 0 }}>Gold Shares Owned</p>
          </div>

          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '12px',
            padding: '20px',
            textAlign: 'center',
            border: '1px solid #374151'
          }}>
            <p style={{ fontSize: '32px', fontWeight: 'bold', color: '#10b981', margin: '0 0 5px 0' }}>
              {"$" + (dataLoading ? '...' : dashboardData.shareValue.toLocaleString())}
            </p>
            <p style={{ fontSize: '14px', color: '#9ca3af', margin: 0 }}>Share Value</p>
          </div>

          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '12px',
            padding: '20px',
            textAlign: 'center',
            border: '1px solid #374151'
          }}>
            <p style={{ fontSize: '32px', fontWeight: 'bold', color: '#f59e0b', margin: '0 0 5px 0' }}>
              {"$" + (dataLoading ? '...' : dashboardData.futureDividends.toLocaleString())}
            </p>
            <p style={{ fontSize: '14px', color: '#9ca3af', margin: 0 }}>Future Dividends</p>
          </div>

          {/* USDT Commissions - Separate like Telegram bot */}
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '12px',
            padding: '20px',
            textAlign: 'center',
            border: '1px solid #374151'
          }}>
            <p style={{ fontSize: '32px', fontWeight: 'bold', color: '#10b981', margin: '0 0 5px 0' }}>
              {"$" + (dataLoading ? '...' : dashboardData.usdtCommissions.totalEarned.toFixed(2))}
            </p>
            <p style={{ fontSize: '14px', color: '#9ca3af', margin: 0 }}>USDT Commissions</p>
          </div>

          {/* Share Commissions - Separate like Telegram bot */}
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '12px',
            padding: '20px',
            textAlign: 'center',
            border: '1px solid #374151'
          }}>
            <p style={{ fontSize: '32px', fontWeight: 'bold', color: '#a855f7', margin: '0 0 5px 0' }}>
              {dataLoading ? '...' : dashboardData.shareCommissions.totalShares.toLocaleString()}
            </p>
            <p style={{ fontSize: '14px', color: '#9ca3af', margin: 0 }}>Share Commissions</p>
          </div>
        </div>

        {/* Dividend Calculator - Shows how dividends are calculated based on user's shares */}
        <DividendCalculator userShares={dashboardData.totalShares} />

        {/* Commission Balance Details - Match Telegram Bot Format */}
        <div style={{
          backgroundColor: 'rgba(31, 41, 55, 0.9)',
          borderRadius: '12px',
          padding: '24px',
          marginBottom: '30px',
          border: '1px solid #374151'
        }}>
          <h3 style={{
            color: 'white',
            fontSize: '20px',
            fontWeight: 'bold',
            margin: '0 0 20px 0',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            💰 COMMISSION BALANCE
          </h3>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '24px'
          }}>
            {/* USDT Commissions Section */}
            <div style={{
              backgroundColor: 'rgba(16, 185, 129, 0.1)',
              borderRadius: '8px',
              padding: '16px',
              border: '1px solid rgba(16, 185, 129, 0.3)'
            }}>
              <h4 style={{ color: '#10b981', fontSize: '16px', fontWeight: '600', margin: '0 0 12px 0' }}>
                💵 USDT COMMISSIONS:
              </h4>
              <div style={{ color: '#d1d5db', fontSize: '14px', lineHeight: '1.6' }}>
                <div style={{ marginBottom: '4px' }}>
                  • <strong>Total Earned:</strong> {"$" + (dataLoading ? '...' : dashboardData.usdtCommissions.totalEarned.toFixed(2))} USDT
                </div>
                <div style={{ marginBottom: '4px' }}>
                  • <strong>Available for Withdrawal:</strong> {"$" + (dataLoading ? '...' : dashboardData.usdtCommissions.available.toFixed(2))} USDT
                </div>
                <div>
                  • <strong>Currently Escrowed:</strong> {"$" + (dataLoading ? '...' : dashboardData.usdtCommissions.escrowed.toFixed(2))} USDT
                </div>
              </div>
            </div>

            {/* Share Commissions Section */}
            <div style={{
              backgroundColor: 'rgba(168, 85, 247, 0.1)',
              borderRadius: '8px',
              padding: '16px',
              border: '1px solid rgba(168, 85, 247, 0.3)'
            }}>
              <h4 style={{ color: '#a855f7', fontSize: '16px', fontWeight: '600', margin: '0 0 12px 0' }}>
                📈 SHARE COMMISSIONS:
              </h4>
              <div style={{ color: '#d1d5db', fontSize: '14px', lineHeight: '1.6' }}>
                <div style={{ marginBottom: '4px' }}>
                  • <strong>Total Shares Earned:</strong> {dataLoading ? '...' : dashboardData.shareCommissions.totalShares.toLocaleString()} shares
                </div>
                <div style={{ marginBottom: '4px' }}>
                  • <strong>Current Value:</strong> {"$" + (dataLoading ? '...' : dashboardData.shareCommissions.currentValue.toFixed(2))} USD
                </div>
                <div>
                  • <strong>Status:</strong> Active in portfolio
                </div>
              </div>
            </div>
          </div>

          {/* Commission Summary */}
          <div style={{
            marginTop: '20px',
            padding: '16px',
            backgroundColor: 'rgba(55, 65, 81, 0.5)',
            borderRadius: '8px',
            border: '1px solid #4b5563'
          }}>
            <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', margin: '0 0 12px 0' }}>
              📊 COMMISSION SUMMARY:
            </h4>
            <div style={{ color: '#d1d5db', fontSize: '14px', lineHeight: '1.6' }}>
              <div style={{ marginBottom: '4px' }}>
                • <strong>Total Commission Value:</strong> {"$" + (dataLoading ? '...' : (dashboardData.usdtCommissions.totalEarned + dashboardData.shareCommissions.currentValue).toFixed(2))}
              </div>
              <div>
                • <strong>Commission Rate:</strong> 15% USDT + 15% Shares
              </div>
            </div>
          </div>
        </div>

        {/* Main Actions */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '20px',
          marginBottom: '30px'
        }}>
          <button
            onClick={() => setShowPurchaseFlow(true)}
            style={{
              background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
              borderRadius: '12px',
              padding: '30px',
              textAlign: 'center',
              border: 'none',
              cursor: 'pointer',
              color: 'white'
            }}>
            <h2 style={{ fontSize: '20px', fontWeight: 'bold', margin: '0 0 8px 0' }}>
              Buy Gold Shares
            </h2>
            <p style={{ fontSize: '14px', opacity: 0.9, margin: 0 }}>
              Purchase shares in our gold mining operation
            </p>
          </button>

          <button
            onClick={() => setShowSharesModal(true)}
            style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '12px',
            padding: '30px',
            textAlign: 'center',
            border: '2px solid #374151',
            cursor: 'pointer',
            color: 'white'
          }}>
            <h2 style={{ fontSize: '20px', fontWeight: 'bold', margin: '0 0 8px 0' }}>
              View My Shares
            </h2>
            <p style={{ fontSize: '14px', color: '#9ca3af', margin: 0 }}>
              Check your current share holdings
            </p>
          </button>
        </div>

        {/* Marketing Toolkit - Available for all users */}
        <div data-section="marketing">
          <MarketingToolkit
            user={user}
            getReferralUsername={getReferralUsername}
          />
        </div>

        {/* Account Info */}
        <div style={{
          backgroundColor: 'rgba(31, 41, 55, 0.9)',
          borderRadius: '12px',
          padding: '20px',
          border: '1px solid #374151'
        }}>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '20px',
            alignItems: 'center'
          }}>
            <div>
              <p style={{ fontSize: '14px', color: '#9ca3af', margin: '0 0 4px 0' }}>Account</p>
              <p style={{ color: 'white', fontWeight: '500', margin: 0 }}>{user?.email}</p>
            </div>
            <div>
              <p style={{ fontSize: '14px', color: '#9ca3af', margin: '0 0 4px 0' }}>Status</p>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ width: '8px', height: '8px', backgroundColor: '#22c55e', borderRadius: '50%' }}></span>
                <p style={{ color: '#22c55e', fontWeight: 'bold', margin: 0 }}>Active</p>
              </div>
            </div>
            <div>
              <p style={{ fontSize: '14px', color: '#9ca3af', margin: '0 0 4px 0' }}>Dividends Start</p>
              <p style={{ color: '#f59e0b', fontWeight: 'bold', margin: 0 }}>March 2026</p>
            </div>
            <div>
              <p style={{ fontSize: '14px', color: '#9ca3af', margin: '0 0 4px 0' }}>Member Since</p>
              <p style={{ color: 'white', fontWeight: '500', margin: 0 }}>
                {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'Today'}
              </p>
            </div>
          </div>
        </div>

        {/* Telegram Connection Section */}
        <div style={{
          backgroundColor: 'rgba(31, 41, 55, 0.9)',
          borderRadius: '12px',
          padding: '24px',
          marginTop: '20px',
          border: '1px solid #374151'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div style={{
                width: '32px',
                height: '32px',
                backgroundColor: '#0088cc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '16px'
              }}>
                📱
              </div>
              <h3 style={{ color: 'white', fontSize: '18px', fontWeight: 'bold', margin: 0 }}>
                Telegram Integration
              </h3>
            </div>
          </div>

          {telegramUser ? (
            // Connected State
            <div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
                <span style={{ width: '8px', height: '8px', backgroundColor: '#22c55e', borderRadius: '50%' }}></span>
                <span style={{ color: '#22c55e', fontWeight: 'bold', fontSize: '14px' }}>Connected to Telegram</span>
              </div>
              <div style={{ marginBottom: '16px' }}>
                <p style={{ fontSize: '14px', color: '#9ca3af', margin: '0 0 4px 0' }}>Connected Account</p>
                <p style={{ color: 'white', fontWeight: '500', margin: 0 }}>
                  {telegramUser.first_name} {telegramUser.last_name}
                  {telegramUser.username && ` (@${telegramUser.username})`}
                </p>
                <p style={{ fontSize: '12px', color: '#6b7280', margin: '2px 0 0 0' }}>
                  ID: {telegramUser.telegram_id}
                </p>
              </div>
              <button
                onClick={handleTelegramDisconnect}
                style={{
                  padding: '8px 16px',
                  backgroundColor: 'transparent',
                  border: '1px solid #ef4444',
                  borderRadius: '6px',
                  color: '#ef4444',
                  fontSize: '12px',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(239, 68, 68, 0.1)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                Disconnect
              </button>
            </div>
          ) : (
            // Not Connected State
            <div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
                <span style={{ width: '8px', height: '8px', backgroundColor: '#6b7280', borderRadius: '50%' }}></span>
                <span style={{ color: '#6b7280', fontSize: '14px' }}>Not connected to Telegram</span>
              </div>
              <p style={{ fontSize: '14px', color: '#9ca3af', marginBottom: '16px', lineHeight: '1.5' }}>
                Connect your Telegram account to sync your existing bot data and enable cross-platform features.
              </p>
              <button
                onClick={() => setShowTelegramModal(true)}
                style={{
                  padding: '12px 20px',
                  backgroundColor: '#0088cc',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = '#0077b3';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = '#0088cc';
                }}
              >
                📱 Connect to Telegram
              </button>
            </div>
          )}
        </div>
          </>
        ) : activeSection === 'referrals' ? (
          <ReferralCenter
            userId={user?.database_user?.id || user?.id || 0}
            className="bg-gray-800 rounded-lg border border-gray-700 p-6"
          />
        ) : activeSection === 'dividends' ? (
          <ComprehensiveDividendsCalculator
            userShares={dashboardData.totalShares}
            currentPhase={null}
          />
        ) : activeSection === 'portfolio' ? (
          <ComprehensivePortfolio
            user={user}
            currentPhase={{ price_per_share: dashboardData.currentSharePrice }}
          />
        ) : activeSection === 'shares' ? (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-2">📊 My Shares</h2>
            <p className="text-gray-400 mb-4">Shares section temporarily unavailable.</p>
            <button
              onClick={() => setShowPurchaseFlow(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Purchase Shares
            </button>
          </div>
        ) : activeSection === 'purchase-shares' ? (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">🛒 Purchase Gold Shares</h2>
            <p className="text-gray-400 mb-6">
              Invest in Aureus Alliance Holdings gold mining shares. Choose your investment amount and payment method.
            </p>
            <button
              onClick={() => setShowPurchaseFlow(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Start Purchase Process
            </button>
          </div>
        ) : activeSection === 'company-presentation' ? (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">📋 Company Presentation</h2>
            <p className="text-gray-400 mb-6">
              Learn about Aureus Alliance Holdings, our mining operations, and investment opportunities.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-2">🏆 About Us</h3>
                <p className="text-gray-300 text-sm">Premium gold mining share ownership opportunity</p>
              </div>
              <div className="bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-2">📊 Investment Details</h3>
                <p className="text-gray-300 text-sm">Comprehensive investment information and projections</p>
              </div>
            </div>
          </div>
        ) : activeSection === 'mining-operations' ? (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">⛏️ Mining Operations</h2>
            <p className="text-gray-400 mb-6">
              View our mining operations, progress updates, and operational metrics.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-gray-700 rounded-lg p-4 text-center">
                <div className="text-2xl mb-2">🏗️</div>
                <h3 className="text-lg font-semibold text-white mb-2">Excavation</h3>
                <p className="text-gray-300 text-sm">Current excavation progress</p>
              </div>
              <div className="bg-gray-700 rounded-lg p-4 text-center">
                <div className="text-2xl mb-2">🔬</div>
                <h3 className="text-lg font-semibold text-white mb-2">Geology</h3>
                <p className="text-gray-300 text-sm">Geological assessments</p>
              </div>
              <div className="bg-gray-700 rounded-lg p-4 text-center">
                <div className="text-2xl mb-2">📈</div>
                <h3 className="text-lg font-semibold text-white mb-2">Production</h3>
                <p className="text-gray-300 text-sm">Production metrics</p>
              </div>
            </div>
          </div>
        ) : activeSection === 'community-relations' ? (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">🏘️ Community Relations</h2>
            <p className="text-gray-400 mb-6">
              Our commitment to community development and stakeholder engagement.
            </p>
            <div className="space-y-4">
              <div className="bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-2">🤝 Community Meetings</h3>
                <p className="text-gray-300 text-sm">Regular engagement with local communities</p>
              </div>
              <div className="bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-2">🏗️ Development Plans</h3>
                <p className="text-gray-300 text-sm">Infrastructure and community development initiatives</p>
              </div>
            </div>
          </div>
        ) : activeSection === 'support-center' ? (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">🆘 Support Center</h2>
            <p className="text-gray-400 mb-6">
              Get help with your account, investments, and platform usage.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-2">📧 Contact Support</h3>
                <p className="text-gray-300 text-sm mb-3">Email: <EMAIL></p>
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm transition-colors">
                  Send Email
                </button>
              </div>
              <div className="bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-2">❓ FAQ</h3>
                <p className="text-gray-300 text-sm mb-3">Frequently asked questions</p>
                <button className="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded text-sm transition-colors">
                  View FAQ
                </button>
              </div>
            </div>
          </div>
        ) : activeSection === 'settings' ? (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">⚙️ Settings</h2>
            <p className="text-gray-400 mb-6">
              Manage your account preferences and security settings.
            </p>
            <div className="space-y-4">
              <div className="bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-2">👤 Profile Settings</h3>
                <p className="text-gray-300 text-sm">Update your personal information</p>
              </div>
              <div className="bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-2">🔒 Security</h3>
                <p className="text-gray-300 text-sm">Password and security preferences</p>
              </div>
              <div className="bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-2">🔔 Notifications</h3>
                <p className="text-gray-300 text-sm">Notification preferences</p>
              </div>
            </div>
          </div>
        ) : activeSection === 'kyc' ? (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">🔒 KYC Verification</h2>
            <p className="text-gray-400 mb-6">
              Complete your identity verification to unlock all platform features.
            </p>
            <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4 mb-4">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-yellow-500">⚠️</span>
                <span className="text-yellow-400 font-medium">Verification Required</span>
              </div>
              <p className="text-yellow-300 text-sm">
                Please complete KYC verification to access all features and increase your transaction limits.
              </p>
            </div>
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
              Start KYC Process
            </button>
          </div>
        ) : activeSection === 'legal-documents' ? (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">📋 Legal Documents</h2>
            <p className="text-gray-400 mb-6">
              Access important legal documents and agreements.
            </p>
            <div className="space-y-3">
              <div className="flex items-center justify-between bg-gray-700 rounded-lg p-4">
                <div>
                  <h3 className="text-white font-medium">Terms & Conditions</h3>
                  <p className="text-gray-400 text-sm">Platform usage terms</p>
                </div>
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm transition-colors">
                  View
                </button>
              </div>
              <div className="flex items-center justify-between bg-gray-700 rounded-lg p-4">
                <div>
                  <h3 className="text-white font-medium">Privacy Policy</h3>
                  <p className="text-gray-400 text-sm">Data protection policy</p>
                </div>
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm transition-colors">
                  View
                </button>
              </div>
              <div className="flex items-center justify-between bg-gray-700 rounded-lg p-4">
                <div>
                  <h3 className="text-white font-medium">Investment Agreement</h3>
                  <p className="text-gray-400 text-sm">Share purchase agreement</p>
                </div>
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm transition-colors">
                  View
                </button>
              </div>
            </div>
          </div>
        ) : (
          // Fallback for any unhandled sections
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">
              {activeSection.charAt(0).toUpperCase() + activeSection.slice(1).replace('-', ' ')}
            </h2>
            <p className="text-gray-400">
              This section is coming soon. Please check back later.
            </p>
          </div>
        )}
      </div>

      </div>

      {/* Share Purchase Flow Modal */}
      {showPurchaseFlow && (
        <SharePurchaseFlow
          user={user}
          onClose={() => setShowPurchaseFlow(false)}
        />
      )}

      {/* Telegram Connection Modal */}
      <TelegramConnectionModal
        isOpen={showTelegramModal}
        onClose={() => setShowTelegramModal(false)}
        onConnect={handleTelegramConnect}
        loading={telegramLoading}
      />

      {/* Shares Modal temporarily disabled to fix build error. */}
      {null}

    </div>
  )
};



export default UserDashboard
